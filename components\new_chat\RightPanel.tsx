'use client';
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai/conversion';
import { Loader } from '@/components/ai/loader';
import { Message, MessageAvatar, MessageContent } from '@/components/ai/message';
import {
  PromptInput,
  PromptInputButton,
  PromptInputModelSelect,
  PromptInputModelSelectContent,
  PromptInputModelSelectItem,
  PromptInputModelSelectTrigger,
  PromptInputModelSelectValue,
  PromptInputSubmit,
  PromptInputTextarea,
  PromptInputToolbar,
  PromptInputTools,
} from '@/components/ai/prompt-input';
import {
  Reasoning,
  ReasoningContent,
  ReasoningTrigger,
} from '@/components/ai/reasoning';
import { Source, Sources, SourcesContent, SourcesTrigger } from '@/components/ai/source';
import { Button } from '@/components/ui/button';
import { MicIcon, PaperclipIcon, RotateCcwIcon, SettingsIcon } from 'lucide-react';
import { type FormEventHandler, useCallback, useState, useEffect } from 'react';
import { toast } from 'sonner';
import { ApiConfigManager, type ModelInfo } from '@/lib/api-config';
import { createApiClient, type ChatMessage as ApiChatMessage } from '@/lib/api-client';
import ApiConfigPanel from './ApiConfigPanel';
type ChatMessage = {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  reasoning?: string;
  sources?: Array<{ title: string; url: string }>;
  isStreaming?: boolean;
};

// 简单 ID 生成器，避免引入新依赖
const genId = () => `${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;


const Example = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: genId(),
      content: "Hello! I'm your AI assistant. I can help you with coding questions, explain concepts, and provide guidance on web development topics. What would you like to know?",
      role: 'assistant',
      timestamp: new Date(),
      sources: [
        { title: "Getting Started Guide", url: "#" },
        { title: "API Documentation", url: "#" }
      ]
    }
  ]);

  const [inputValue, setInputValue] = useState('');
  const [selectedModel, setSelectedModel] = useState('no-models');
  const [isTyping, setIsTyping] = useState(false);
  const [showApiConfig, setShowApiConfig] = useState(false);
  const [availableModels, setAvailableModels] = useState<ModelInfo[]>([]);

  const configManager = ApiConfigManager.getInstance();

  // 加载可用模型列表
  useEffect(() => {
    const models = configManager.getSelectedModels();
    console.log('初始加载模型列表:', models);
    setAvailableModels(models);
    if (models.length > 0 && (selectedModel === 'no-models' || !selectedModel)) {
      const newSelectedModel = `${models[0].provider}::${models[0].id}`;
      console.log('设置初始选中模型:', newSelectedModel);
      setSelectedModel(newSelectedModel);
    } else if (models.length === 0) {
      console.log('没有可用模型，设置为no-models');
      setSelectedModel('no-models');
    }
  }, []); // 移除selectedModel依赖，只在组件挂载时执行一次

  // 监听showApiConfig变化，当从配置页面返回时重新加载模型
  useEffect(() => {
    if (!showApiConfig) {
      const models = configManager.getSelectedModels();
      console.log('从配置页面返回，重新加载模型:', models);
      setAvailableModels(models);
      if (models.length === 0) {
        console.log('从配置返回后没有模型，设置为no-models');
        setSelectedModel('no-models');
      } else {
        // 若当前未选中或选中的模型不在列表中，自动选中第一个
        const exists = models.find(m => `${m.provider}::${m.id}` === selectedModel);
        if (!exists) {
          const newSelectedModel = `${models[0].provider}::${models[0].id}`;
          console.log('从配置返回后自动选中模型:', newSelectedModel);
          setSelectedModel(newSelectedModel);
        }
      }
    }
  }, [showApiConfig, selectedModel]);

  // 当可用模型列表变化时，若未选中有效模型则自动选择第一个
  useEffect(() => {
    if (availableModels.length > 0) {
      const exists = availableModels.find(m => `${m.provider}::${m.id}` === selectedModel);
      if (!exists) {
        const newSelectedModel = `${availableModels[0].provider}::${availableModels[0].id}`;
        console.log('可用模型变更后自动选中:', newSelectedModel);
        setSelectedModel(newSelectedModel);
      }
    }
  }, [availableModels]);

  // 获取当前选中的模型信息
  const getCurrentModel = useCallback(() => {
    if (!selectedModel || selectedModel === 'no-models') return null;
    const [provider, modelId] = selectedModel.split('::', 2);
    return availableModels.find(m => m.provider === provider && m.id === modelId);
  }, [selectedModel, availableModels]);

  // 获取当前模型的API配置
  const getCurrentApiConfig = useCallback(() => {
    const model = getCurrentModel();
    if (!model) return null;
    // 优先按提供商精确匹配；若未保存配置，回退到任意已保存配置（提高容错）
    return (
      configManager.getApiConfig(model.provider) ||
      Array.from(configManager.getAllConfigs().values())[0] ||
      null
    );
  }, [getCurrentModel, configManager]);


  // 真实的API聊天处理
  const handleSubmit: FormEventHandler<HTMLFormElement> = useCallback(async (event) => {
    event.preventDefault();

    if (!inputValue.trim() || isTyping) return;

    let model = getCurrentModel();
    let apiConfig = getCurrentApiConfig();

    if (!model || !apiConfig) {
      // 若未选择但有可用模型，自动选择第一个并继续
      if (availableModels.length > 0) {
        const auto = `${availableModels[0].provider}::${availableModels[0].id}`;
        setSelectedModel(auto);
        // 重新获取
        const m = availableModels[0];
        const cfg = configManager.getApiConfig(m.provider);
        if (cfg) {
          // 不再跳配置页，继续走后续流程
          model = m;
          apiConfig = cfg;
        } else {
          toast.error('请先配置API并选择模型');
          setShowApiConfig(true);
          return;
        }
      } else {
        toast.error('请先配置API并选择模型');
        setShowApiConfig(true);
        return;
      }
    }

    // Add user message
    const userMessage: ChatMessage = {
      id: genId(),
      content: inputValue.trim(),
      role: 'user',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Create assistant message for streaming
    const assistantMessageId = genId();
    const assistantMessage: ChatMessage = {
      id: assistantMessageId,
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      isStreaming: true,
    };
    setMessages(prev => [...prev, assistantMessage]);

    try {
      const client = createApiClient(apiConfig);

      // 准备聊天历史
      const chatMessages: ApiChatMessage[] = messages
        .filter(m => !m.isStreaming)
        .map(m => ({
          role: m.role,
          content: m.content,
        }));

      // 添加当前用户消息
      chatMessages.push({
        role: 'user',
        content: userMessage.content,
      });

      // 流式响应处理
      let fullContent = '';
      let reasoning = '';
      let isInReasoningBlock = false;

      for await (const chunk of client.chatCompletionStream({
        model: model.id,
        messages: chatMessages,
        temperature: 0.7,
        max_tokens: 2000,
      })) {
        const delta = chunk.choices?.[0]?.delta;
        if (delta?.content) {
          const content = delta.content;

          // 检测思考过程标记
          if (content.includes('<thinking>')) {
            isInReasoningBlock = true;
          }

          if (isInReasoningBlock) {
            reasoning += content;
            if (content.includes('</thinking>')) {
              isInReasoningBlock = false;
              // 提取思考内容
              const thinkingMatch = reasoning.match(/<thinking>([\s\S]*?)<\/thinking>/);
              if (thinkingMatch) {
                reasoning = thinkingMatch[1].trim();
              }
            }
          } else {
            fullContent += content;
          }

          // 更新消息内容
          setMessages(prev => prev.map(msg =>
            msg.id === assistantMessageId
              ? {
                  ...msg,
                  content: fullContent,
                  reasoning: reasoning || undefined,
                  isStreaming: chunk.choices?.[0]?.finish_reason !== 'stop'
                }
              : msg
          ));
        }

        if (chunk.choices?.[0]?.finish_reason === 'stop') {
          break;
        }
      }
    } catch (error) {
      console.error('聊天请求失败:', error);
      toast.error('聊天请求失败，请检查网络连接和API配置');

      // 移除失败的消息
      setMessages(prev => prev.filter(msg => msg.id !== assistantMessageId));
    } finally {
      setIsTyping(false);
    }
  }, [inputValue, isTyping, getCurrentModel, getCurrentApiConfig, messages]);
  const handleReset = useCallback(() => {
    setMessages([
      {
        id: genId(),
        content: "Hello! I'm your AI assistant. I can help you with coding questions, explain concepts, and provide guidance on web development topics. What would you like to know?",
        role: 'assistant',
        timestamp: new Date(),
        sources: [
          { title: "Getting Started Guide", url: "#" },
          { title: "API Documentation", url: "#" }
        ]
      }
    ]);
    setInputValue('');
    setIsTyping(false);
  }, []);
  // 处理配置保存后的回调
  const handleConfigSaved = useCallback(() => {
    const models = configManager.getSelectedModels();
    console.log('配置保存后获取的模型:', models);
    setAvailableModels(models);
    if (models.length > 0) {
      // 如果当前没有选中有效模型，选择第一个
      const currentModel = getCurrentModel();
      console.log('当前选中的模型:', currentModel);
      if (!currentModel) {
        const newSelectedModel = `${models[0].provider}::${models[0].id}`;
        console.log('配置保存后设置新模型:', newSelectedModel);
        setSelectedModel(newSelectedModel);
      }
    } else {
      console.log('配置保存后没有模型');
      setSelectedModel('no-models');
    }
  }, [getCurrentModel]);

  // 如果显示API配置面板
  if (showApiConfig) {
    return (
      <ApiConfigPanel
        onBack={() => setShowApiConfig(false)}
        onConfigSaved={handleConfigSaved}
      />
    );
  }

  return (
    <div className="flex h-full w-full flex-col overflow-hidden rounded-xl border bg-background shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between border-b bg-muted/50 px-4 py-3">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className="size-2 rounded-full bg-green-500" />
            <span className="font-medium text-sm">AI Assistant</span>
          </div>
          <div className="h-4 w-px bg-border" />
          <span className="text-muted-foreground text-xs">
            {getCurrentModel()?.name || '未选择模型'}
          </span>
        </div>
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowApiConfig(true)}
            className="h-8 px-2"
          >
            <SettingsIcon className="size-4" />
            <span className="ml-1">配置</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-8 px-2"
          >
            <RotateCcwIcon className="size-4" />
            <span className="ml-1">Reset</span>
          </Button>
        </div>
      </div>
      {/* Conversation Area */}
      <Conversation className="flex-1">
        <ConversationContent className="space-y-4">
          {messages.map((message) => (
            <div key={message.id} className="space-y-3">
              <Message from={message.role}>
                <MessageContent>
                  {message.isStreaming && message.content === '' ? (
                    <div className="flex items-center gap-2">
                      <Loader size={14} />
                      <span className="text-muted-foreground text-sm">Thinking...</span>
                    </div>
                  ) : (
                    message.content
                  )}
                </MessageContent>
                <MessageAvatar
                  src={message.role === 'user' ? 'https://github.com/dovazencot.png' : 'https://github.com/vercel.png'}
                  name={message.role === 'user' ? 'User' : 'AI'}
                />
              </Message>
              {/* Reasoning */}
              {message.reasoning && (
                <div className="ml-10">
                  <Reasoning isStreaming={message.isStreaming} defaultOpen={false}>
                    <ReasoningTrigger />
                    <ReasoningContent>{message.reasoning}</ReasoningContent>
                  </Reasoning>
                </div>
              )}
              {/* Sources */}
              {message.sources && message.sources.length > 0 && (
                <div className="ml-10">
                  <Sources>
                    <SourcesTrigger count={message.sources.length} />
                    <SourcesContent>
                      {message.sources.map((source, index) => (
                        <Source key={index} href={source.url} title={source.title} />
                      ))}
                    </SourcesContent>
                  </Sources>
                </div>
              )}
            </div>
          ))}
        </ConversationContent>
        <ConversationScrollButton />
      </Conversation>
      {/* Input Area */}
      <div className="border-t p-4">
        <PromptInput onSubmit={handleSubmit}>
          <PromptInputTextarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Ask me anything about development, coding, or technology..."
            disabled={isTyping}
          />
          <PromptInputToolbar>
            <PromptInputTools>
              <PromptInputButton disabled={isTyping}>
                <PaperclipIcon size={16} />
              </PromptInputButton>
              <PromptInputButton disabled={isTyping}>
                <MicIcon size={16} />
                <span>Voice</span>
              </PromptInputButton>
              <PromptInputModelSelect
                value={selectedModel}
                onValueChange={setSelectedModel}
                disabled={isTyping}
              >
                <PromptInputModelSelectTrigger>
                  <PromptInputModelSelectValue />
                </PromptInputModelSelectTrigger>
                <PromptInputModelSelectContent>
                  {availableModels.length > 0 ? (
                    availableModels.map((model) => (
                      <PromptInputModelSelectItem
                        key={`${model.provider}::${model.id}`}
                        value={`${model.provider}::${model.id}`}
                      >
                        {model.name}
                      </PromptInputModelSelectItem>
                    ))
                  ) : (
                    <PromptInputModelSelectItem value="no-models" disabled>
                      请先配置API并添加模型
                    </PromptInputModelSelectItem>
                  )}
                </PromptInputModelSelectContent>
              </PromptInputModelSelect>
            </PromptInputTools>
            <PromptInputSubmit
              disabled={!inputValue.trim() || isTyping}
              status={isTyping ? 'streaming' : undefined}
            />
          </PromptInputToolbar>
        </PromptInput>
      </div>
    </div>
  );
};
export default Example;