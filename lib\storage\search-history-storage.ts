/**
 * 搜索历史本地存储管理
 * 提供搜索历史的增删改查功能，支持本地IndexedDB存储
 */

import type { 
  UserSearchHistory, 
  CreateSearchHistoryData, 
  SearchHistoryItem,
  SearchFilters 
} from '@/types/database/search_history';

// 本地搜索历史记录接口（包含本地ID）
export interface LocalSearchHistory extends Omit<UserSearchHistory, 'id'> {
  local_id: string;               // 本地唯一ID
  id?: number;                    // 保留字段（兼容性）
  status: 'active' | 'deleted';   // 数据状态（仅用于软删除）
}

// IndexedDB 配置
const DB_NAME = 'AxsightSearchHistory';
const DB_VERSION = 1;
const HISTORY_STORE = 'search_history';

class SearchHistoryStorage {
  private db: IDBDatabase | null = null;

  // 确保在浏览器环境中运行
  private ensureBrowserEnvironment() {
    if (typeof window === 'undefined') {
      throw new Error('搜索历史存储只能在浏览器环境中使用');
    }
  }

  // 初始化数据库
  private async initDB(): Promise<IDBDatabase> {
    this.ensureBrowserEnvironment();
    
    if (this.db) {
      return this.db;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // 创建搜索历史存储
        if (!db.objectStoreNames.contains(HISTORY_STORE)) {
          const store = db.createObjectStore(HISTORY_STORE, { keyPath: 'local_id' });
          
          // 创建索引
          store.createIndex('user_id', 'user_id', { unique: false });
          store.createIndex('search_query', 'search_query', { unique: false });
          store.createIndex('created_at', 'created_at', { unique: false });
          store.createIndex('status', 'status', { unique: false });
        }
      };
    });
  }

  // 生成本地唯一ID
  private generateLocalId(): string {
    return `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 创建搜索历史记录
  async createHistory(historyData: CreateSearchHistoryData): Promise<LocalSearchHistory> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([HISTORY_STORE], 'readwrite');
    const store = transaction.objectStore(HISTORY_STORE);

    const now = new Date().toISOString();
    const newHistory: LocalSearchHistory = {
      local_id: this.generateLocalId(),
      ...historyData,
      created_at: now,
      updated_at: now,
      status: 'active',
    };

    return new Promise((resolve, reject) => {
      const request = store.add(newHistory);
      request.onsuccess = () => resolve(newHistory);
      request.onerror = () => reject(request.error);
    });
  }

  // 获取用户的搜索历史（按时间倒序）
  async getHistoryByUser(userId: number, limit: number = 50): Promise<SearchHistoryItem[]> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([HISTORY_STORE], 'readonly');
    const store = transaction.objectStore(HISTORY_STORE);
    const index = store.index('user_id');

    return new Promise((resolve, reject) => {
      const request = index.getAll(userId);
      request.onsuccess = () => {
        const histories = request.result
          .filter(history => history.status !== 'deleted')
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, limit)
          .map(history => ({
            id: history.id || 0,
            search_query: history.search_query,
            search_filters: history.search_filters,
            result_count: history.result_count,
            result_data: history.result_data,
            created_at: history.created_at,
          }));
        resolve(histories);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 根据关键词搜索历史记录
  async searchHistory(userId: number, query: string): Promise<SearchHistoryItem[]> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([HISTORY_STORE], 'readonly');
    const store = transaction.objectStore(HISTORY_STORE);
    const index = store.index('user_id');

    return new Promise((resolve, reject) => {
      const request = index.getAll(userId);
      request.onsuccess = () => {
        const histories = request.result
          .filter(history =>
            history.status !== 'deleted' &&
            history.search_query.toLowerCase().includes(query.toLowerCase())
          )
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .map(history => ({
            id: history.id || 0,
            search_query: history.search_query,
            search_filters: history.search_filters,
            result_count: history.result_count,
            result_data: history.result_data,
            created_at: history.created_at,
          }));
        resolve(histories);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 删除搜索历史记录
  async deleteHistory(localId: string): Promise<void> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([HISTORY_STORE], 'readwrite');
    const store = transaction.objectStore(HISTORY_STORE);

    return new Promise((resolve, reject) => {
      // 软删除：标记为已删除状态
      const getRequest = store.get(localId);
      getRequest.onsuccess = () => {
        const history = getRequest.result;
        if (history) {
          history.status = 'deleted';
          history.updated_at = new Date().toISOString();
          
          const updateRequest = store.put(history);
          updateRequest.onsuccess = () => resolve();
          updateRequest.onerror = () => reject(updateRequest.error);
        } else {
          resolve(); // 记录不存在，视为删除成功
        }
      };
      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  // 清空用户的所有搜索历史
  async clearUserHistory(userId: number): Promise<void> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([HISTORY_STORE], 'readwrite');
    const store = transaction.objectStore(HISTORY_STORE);
    const index = store.index('user_id');

    return new Promise((resolve, reject) => {
      const request = index.getAll(userId);
      request.onsuccess = () => {
        const histories = request.result.filter(h => h.status !== 'deleted');
        const deletePromises = histories.map(history => {
          history.status = 'deleted';
          history.updated_at = new Date().toISOString();
          return new Promise<void>((res, rej) => {
            const updateRequest = store.put(history);
            updateRequest.onsuccess = () => res();
            updateRequest.onerror = () => rej(updateRequest.error);
          });
        });

        Promise.all(deletePromises)
          .then(() => resolve())
          .catch(reject);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 获取特定搜索历史的详细信息（包含结果ID）
  async getHistoryDetail(localId: string): Promise<LocalSearchHistory | null> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([HISTORY_STORE], 'readonly');
    const store = transaction.objectStore(HISTORY_STORE);

    return new Promise((resolve, reject) => {
      const request = store.get(localId);
      request.onsuccess = () => {
        const history = request.result;
        if (history && history.status !== 'deleted') {
          resolve(history);
        } else {
          resolve(null);
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 检查是否存在相同的搜索记录
  async findSimilarHistory(
    userId: number, 
    query: string, 
    filters: SearchFilters
  ): Promise<LocalSearchHistory | null> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([HISTORY_STORE], 'readonly');
    const store = transaction.objectStore(HISTORY_STORE);
    const index = store.index('user_id');

    return new Promise((resolve, reject) => {
      const request = index.getAll(userId);
      request.onsuccess = () => {
        const similarHistory = request.result.find(history =>
          history.status !== 'deleted' &&
          history.search_query === query &&
          JSON.stringify(history.search_filters) === JSON.stringify(filters)
        );
        resolve(similarHistory || null);
      };
      request.onerror = () => reject(request.error);
    });
  }
}

// 创建安全的包装器类
class SafeSearchHistoryStorage {
  private storage: SearchHistoryStorage | null = null;

  constructor() {
    if (typeof window !== 'undefined') {
      this.storage = new SearchHistoryStorage();
    }
  }

  private ensureStorage(): SearchHistoryStorage {
    if (!this.storage) {
      throw new Error('搜索历史存储在服务端环境中不可用');
    }
    return this.storage;
  }

  async createHistory(historyData: CreateSearchHistoryData): Promise<LocalSearchHistory> {
    return this.ensureStorage().createHistory(historyData);
  }

  async getHistoryByUser(userId: number, limit?: number): Promise<SearchHistoryItem[]> {
    return this.ensureStorage().getHistoryByUser(userId, limit);
  }

  async searchHistory(userId: number, query: string): Promise<SearchHistoryItem[]> {
    return this.ensureStorage().searchHistory(userId, query);
  }

  async deleteHistory(localId: string): Promise<void> {
    return this.ensureStorage().deleteHistory(localId);
  }

  async clearUserHistory(userId: number): Promise<void> {
    return this.ensureStorage().clearUserHistory(userId);
  }

  async getHistoryDetail(localId: string): Promise<LocalSearchHistory | null> {
    return this.ensureStorage().getHistoryDetail(localId);
  }

  async findSimilarHistory(
    userId: number, 
    query: string, 
    filters: SearchFilters
  ): Promise<LocalSearchHistory | null> {
    return this.ensureStorage().findSimilarHistory(userId, query, filters);
  }
}

// 导出单例实例
export const searchHistoryStorage = new SafeSearchHistoryStorage();
export default searchHistoryStorage;
