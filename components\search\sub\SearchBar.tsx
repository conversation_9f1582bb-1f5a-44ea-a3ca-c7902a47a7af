"use client";

import React, { useEffect, useRef, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useSearchContext } from '@/context/SearchContext';
import { useBookmarkContext } from '@/context/BookmarkContext';
import { LuSearch } from "react-icons/lu";
import { SourceType } from '@/types/database/enums';
import { normalizePaperData } from '@/lib/utils/paper-utils';

export default function SearchBar() {
  const {
    papers, setPapers,
    loading, setLoading,
    loadingStr, setLoadingStr,
    query, setQuery,
    source, setSource,
  } = useSearchContext();

  const { updateItemTranslation } = useBookmarkContext();

  // 是否进行标题翻译
  const [isTitleTranslation, setIsTitleTranslation] = useState(true);

  // 支持

  const handleSearch = async () => {
    setLoading(true);
    try {
      const res = await fetch(`/api/semantic-search?query=${encodeURIComponent(query)}&source=${source}`);
      const data = await res.json();
      console.log('data', data)
      // 规范化论文数据，确保 authors 等字段是字符串格式
      const normalizedPapers = (data || []).map(normalizePaperData);
      setPapers(normalizedPapers);

    } catch (e) {
      setPapers([]);
    }
    console.log('papers', papers)
    setLoading(false);
  };

 // 检查所有搜索结果的标题翻译
 useEffect(() => {
  const translateTitles = async () => {
    if (!isTitleTranslation) return;

    const needTranslation = papers.map((paper, index) => ({
      index,
      needsTranslation: !paper.translated_title,
      title: paper.title
    })).filter(item => item.needsTranslation);

    if (needTranslation.length === 0) return;

    try {
      setLoadingStr("翻译中...")

      const translationResponse = await fetch('/api/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: needTranslation.map(item => item.title)
        }),
      });

      if (!translationResponse.ok) {
        throw new Error(`标题翻译请求失败: ${translationResponse.statusText}`);
      }

      const data = await translationResponse.json();
      
      if (!data || !data.translation) {
        console.error('标题翻译返回数据格式错误:', data);
        return;
      }

      const translations = Array.isArray(data.translation) 
        ? data.translation 
        : JSON.parse(data.translation);

      if (translations.length !== needTranslation.length) {
        console.error('标题翻译返回数量不匹配');
        return;
      }

      setPapers(prev => {
        const newResults = [...prev];
        needTranslation.forEach((item, arrayIndex) => {
          if (translations[arrayIndex]) {
            const updatedPaper = {
              ...newResults[item.index],
              translated_title: translations[arrayIndex]
            };
            newResults[item.index] = updatedPaper;

            // 同时更新已收藏的item（如果存在的话）
            const sourceId = updatedPaper.pdf_url || updatedPaper.title;
            updateItemTranslation(sourceId, SourceType.ARXIV, translations[arrayIndex]).catch(error => {
              console.warn('更新收藏item翻译失败:', error);
            });
          }
        });
        return newResults;
      });

    } catch (error) {
      console.error('标题翻译失败:', error);
    } finally {
      setLoading(false);
      setLoadingStr("搜索中...")
    }
  };
  translateTitles();
}, [papers, isTitleTranslation, setLoading]);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-2 items-center">
        <div className="relative w-80">
          <LuSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 size-4" />
          <Input
            placeholder="请输入检索内容"
            value={query}
            onChange={e => setQuery(e.target.value)}
            onKeyDown={e => e.key === 'Enter' && handleSearch()}
            className="pl-10 h-10"
          />
        </div>
      </div>
      <div className="flex items-center gap-2 justify-between w-full">
        <Select value={source} onValueChange={setSource}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="arxiv">arXiv预印本</SelectItem>
            <SelectItem value="conference">已发表会议</SelectItem>
            <SelectItem value="journal">已发表期刊</SelectItem>
          </SelectContent>
        </Select>

        <Button
          loading={loading}
          onClick={handleSearch}
          // className="bg-blue-100 text-blue-700 hover:bg-blue-200 border-none shadow-none ml-4"
        >
          搜索
        </Button>
      </div>
    </div>
  );
}
