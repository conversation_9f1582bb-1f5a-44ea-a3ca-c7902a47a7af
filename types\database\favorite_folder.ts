export interface FavoriteFolder {
  id: number; // 收藏夹ID
  user_id: number; // 所属用户ID
  parent_id?: number; // 上级收藏夹ID，顶级为NULL
  name: string; // 收藏夹名称
  icon: string; // 文件夹图标标识（默认folder）
  color: string; // 文件夹颜色（默认蓝色）
  // is_default?: boolean; // 是否为默认文件夹
  is_public: boolean; // 是否公开（用于分享功能）
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
}

// 用于创建收藏夹的类型（不包含自增ID和时间戳）
export type CreateFavoriteFolderData = Omit<FavoriteFolder, 'id' | 'created_at' | 'updated_at'>;

// 用于更新收藏夹的类型（所有字段可选，除了ID）
export type UpdateFavoriteFolderData = Partial<Omit<FavoriteFolder, 'id' | 'created_at' | 'updated_at'>> & {
  id: number;
};
