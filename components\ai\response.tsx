'use client'

import { cn } from '@/lib/utils'
import type { HTMLAttributes } from 'react'
import { renderMarkdownToJsx } from '@/components/search/sub/MarkdownToJsx'

export type ResponseProps = HTMLAttributes<HTMLDivElement>

export function Response({ className, children, ...props }: ResponseProps) {
  return (
    <div className={cn('prose dark:prose-invert max-w-none', className)} {...props}>
      {typeof children === 'string' ? renderMarkdownToJsx(children) : children}
    </div>
  )
}

