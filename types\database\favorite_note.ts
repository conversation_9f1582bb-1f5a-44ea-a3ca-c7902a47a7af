export interface FavoriteNote {
  id: number; // 笔记ID
  user_id: number; // 所属用户ID
  favorite_item_id: number; // 关联的收藏项目ID
  title: string; // 笔记标题
  content?: string; // 笔记内容（Markdown格式）
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
}

// 用于创建笔记的类型（不包含自增ID和时间戳）
export type CreateFavoriteNoteData = Omit<FavoriteNote, 'id' | 'created_at' | 'updated_at'>;

// 用于更新笔记的类型（所有字段可选，除了ID）
export type UpdateFavoriteNoteData = Partial<Omit<FavoriteNote, 'id' | 'created_at' | 'updated_at'>> & {
  id: number;
};
