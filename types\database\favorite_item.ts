// 收藏项目类型定义
export interface FavoriteItem {
  id: number; // 收藏项目ID
  user_id: number; // 所属用户ID
  folder_id: number; // 所属收藏夹ID
  
  // 内容类型和来源
  item_type: string; // 项目类型（默认note）
  source_id?: string; // 原始数据源中的ID
  source_type?: string; // 数据源类型：vector_db, arxiv, pubmed, user_upload, web, google scholar等
  
  // 基础信息（适用于所有类型）
  title: string; // 标题
  authors?: string; // 作者(逗号分隔)
  summary?: string; // 摘要
  sub_summary?: string; // 摘要的进一步精简
  keywords?: string; // 关键词(逗号分隔)
  published?: string; // 发表日期
  source?: string; // 来源（期刊、会议、网站等）
  doi?: string; // DOI标识符
  
  // 文件相关
  file_url?: string; // 文件存储路径
  file_size?: number; // 文件大小（字节）
  file_type?: string; // 文件类型（pdf, doc, etc.）
  
  // 用户自定义信息
  custom_title?: string; // 用户自定义标题
  user_notes?: string; // 用户备注
  user_tags?: string; // 用户标签（逗号分隔）

  // 翻译信息
  translated_title?: string; // 中文标题
  translated_summary?: string; // 中文摘要
  
  created_at: string; // 收藏时间
  updated_at: string; // 更新时间
}

// 项目类型和数据源类型枚举在 enums.ts 中定义

// 用于创建收藏项目的类型（不包含自增ID和时间戳）
export type CreateFavoriteItemData = Omit<FavoriteItem, 'id' | 'created_at' | 'updated_at'>;

// 用于更新收藏项目的类型（所有字段可选，除了ID）
export type UpdateFavoriteItemData = Partial<Omit<FavoriteItem, 'id' | 'created_at' | 'updated_at'>> & {
  id: number;
};

// 收藏项目的前端展示类型（包含额外的计算字段）
export interface FavoriteItemWithExtras extends FavoriteItem {
  // 解析后的标签数组
  tags?: string[];
  // 解析后的作者数组
  authorList?: string[];
  // 解析后的关键词数组
  keywordList?: string[];
  // 文件大小的人类可读格式
  fileSizeFormatted?: string;
  // 是否为论文类型
  isPaper?: boolean;
  // 是否为用户上传的文件
  isUserUpload?: boolean;
}
