'use client';

import React, { useMemo } from 'react';
import { TooltipS } from '@/components/ui/tooltip-simple';
import { SIDEBAR_TITLES } from '@/types/ui';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { MessageCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import IconSidebar from '@/components/IconSideBar';
import LeftPanel from '@/components/LeftPanelCore';
import MainPanel from '@/components/MainPanelCore';
import RightPanel from '@/components/RightPanelCore';
import { useIconSidebarContext } from '@/context/IconSideBarContext';
import type { SidebarKey } from '@/types/ui';

const HomePage: React.FC = () => {
  // 管理侧栏的开关以及当前 icon 的选择
  const {
    selectedKey, setSelectedKey,
    leftVisible, setLeftVisible,
    rightVisible, setRightVisible,
  } = useIconSidebarContext();

  // 功能标题映射 - 使用 useMemo 优化
  const getTitleByKey = useMemo(() => {
    return (key: SidebarKey) => SIDEBAR_TITLES[key] ?? '功能面板';
  }, []);

  // 处理图标选择
  const handleIconSelect = (key: SidebarKey) => {
    setSelectedKey(key);
    // 如果左侧栏关闭，选择图标时重新打开
    if (!leftVisible) {
      setLeftVisible(true);
    }
  };

  // 处理左侧栏关闭
  const handleLeftClose = () => {
    setLeftVisible(false);
    setSelectedKey(''); // 清除选中的 key
  };

  return (
    <div style={{ height: '100vh', overflow: 'hidden', flexDirection: 'row', display: 'flex' }}>
      {/* 最左侧 icon 栏 */}
      <IconSidebar selectedKey={selectedKey} onSelect={handleIconSelect} />

      {/* 右侧三栏：左栏、主栏、右栏，可拖动宽度 */}
      <div style={{ flex: 1, height: '100vh', overflow: 'hidden', background: 'white'}}>
        <PanelGroup direction="horizontal" style={{ height: '100vh' }}>

          {/* 左侧第二栏 */}
          {leftVisible && (
            <Panel defaultSize={18} minSize={10} maxSize={50} order={1}>
              <div style={{ position: 'relative', height: '100%' }}>
                <div
                  style={{
                    position: 'absolute',
                    top: 0,
                    right: 10,
                    bottom: 0,
                    left: 0,
                    borderRadius: 0,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.12)',
                    background: 'white',
                    overflow: 'hidden',
                    borderLeft: '1px solid #eee',
                    zIndex: 10,
                    display: 'flex',
                    flexDirection: 'column',
                    height: 'auto',
                  }}
                >
                  {/* 头部：标题和关闭按钮 */}
                  <div className="flex justify-between items-center p-4 border-b border-gray-100">
                    <span className="text-base font-semibold text-gray-800">
                      {getTitleByKey(selectedKey)}
                    </span>
                    <TooltipS content="关闭左侧栏" side="bottom">
                      <button
                        onClick={handleLeftClose}
                        className="w-8 h-8 rounded-full bg-white shadow flex items-center justify-center z-50 cursor-pointer text-xl text-gray-800 hover:shadow-lg leading-none"
                      >
                        ×
                      </button>
                    </TooltipS>
                  </div>

                  {/* 面板内容 */}
                  <div className="flex-1 overflow-hidden">
                    <LeftPanel />
                  </div>
                </div>
              </div>
            </Panel>
          )}

          {leftVisible && <PanelResizeHandle style={{ width: '0px', background: '#d9d9d9', cursor: 'col-resize', position: 'relative', right: '10px', zIndex: 20}} />}

          {/* 主内容栏 */}
          <Panel order={2} defaultSize={rightVisible ? 60 : 85} minSize={30}>
            <div style={{ background: 'white', height: '100%' }}>
              <MainPanel />
            </div>
          </Panel>

          {rightVisible && <PanelResizeHandle style={{ width: '0px', background: '#d9d9d9', cursor: 'col-resize', position: 'relative', left: '10px', zIndex: 20 }} />}

          {/* 右侧栏 */}
          {rightVisible && (
            <Panel order={3} defaultSize={24} minSize={12} maxSize={50}>
              <div style={{ position: 'relative', height: '100%' }}>
                {/* 关闭按钮，悬浮在右上角 */}
                <button
                  onClick={() => setRightVisible(false)}
                  className="
                  absolute top-4 right-4 w-8 h-8 rounded-full bg-white shadow flex items-center justify-center
                  z-50 cursor-pointer text-xl text-gray-800 hover:shadow-lg
                  dark:bg-gray-800 dark:text-gray-100 leading-none"
                >
                  ×
                </button>
                <div className="
                    absolute top-2 right-2 bottom-2 left-[10px] rounded-2xl shadow-lg bg-white
                    overflow-hidden border-l border-[#eee] z-10 flex flex-col h-auto
                    dark:bg-gray-800"
                >
                  <RightPanel />
                </div>
              </div>
            </Panel>
          )}

        </PanelGroup>
      </div>

      {/* 右下角聊天按钮 - 仅在右侧面板关闭时显示 */}
      <AnimatePresence>
        {!rightVisible && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 25,
              duration: 0.2
            }}
          >
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setRightVisible(true)}
              className="fixed w-14 h-14 rounded-full bg-gray-800 hover:bg-gray-700 text-white shadow-lg flex items-center justify-center cursor-pointer border-none outline-none"
              style={{
                right: 24,
                bottom: 24,
                zIndex: 1000,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
              }}
              title="打开聊天面板"
            >
              <MessageCircle size={20} color="white" />
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default HomePage;
