// 论文类型定义（用于向量数据库对接）
export interface Paper {
  id: number; // 论文ID
  title: string; // 标题
  authors?: string; // 作者
  summary?: string; // 摘要
  keywords?: string; // 关键词
  published?: string; // 发表日期
  source?: string; // 来源
  doi?: string; // DOI
  pdf_url?: string; // PDF链接
  translated_title?: string; // 中文标题
  translated_summary?: string; // 中文摘要
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
}

// 带有额外字段的论文类型
export interface PaperWithExtras extends Paper {
  // 其他扩展字段
  file_size?: number;
  file_type?: string;
}
