'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { User, LogOut, Settings } from 'lucide-react';
import { useUserContext } from '@/context/UserContext';

export default function UserInfoPanel() {
  const { user, logout } = useUserContext();

  if (!user) {
    return null; // 如果没有用户信息，不显示此面板
  }

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="p-6 h-full bg-white">
      <Card className="shadow-sm">
        <CardContent className="p-6">
          <div className="text-center mb-6">
            <Avatar className="w-20 h-20 mb-4 mx-auto">
              <AvatarImage src={user.avatar_url || 'https://api.dicebear.com/7.x/pixel-art/svg?seed=cat'} />
              <AvatarFallback>
                <User size={32} />
              </AvatarFallback>
            </Avatar>
            <h4 className="text-lg font-semibold mb-2">{user.username}</h4>
            <p className="text-muted-foreground text-sm mb-3">{user.email}</p>
          </div>

          <div className="border-t border-gray-200 my-4"></div>

          <div className="space-y-4">
            <div>
              <span className="font-medium">用户角色：</span>
              <span className="ml-2">{user.role}</span>
            </div>

            {user.phone && (
              <div>
                <span className="font-medium">手机号：</span>
                <span className="ml-2">{user.phone}</span>
              </div>
            )}

            <div>
              <span className="font-medium">注册时间：</span>
              <span className="ml-2">
                {new Date(user.created_at).toLocaleDateString()}
              </span>
            </div>

            {user.last_login_at && (
              <div>
                <span className="font-medium">最后登录：</span>
                <span className="ml-2">
                  {new Date(user.last_login_at).toLocaleString()}
                </span>
              </div>
            )}
          </div>

          <div className="border-t border-gray-200 my-4"></div>

          <div className="space-y-2">
            <Button
              variant="outline"
              className="w-full"
            >
              <Settings className="mr-2 h-4 w-4" />
              账户设置
            </Button>
            <Button
              variant="destructive"
              onClick={handleLogout}
              className="w-full"
            >
              <LogOut className="mr-2 h-4 w-4" />
              退出登录
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}