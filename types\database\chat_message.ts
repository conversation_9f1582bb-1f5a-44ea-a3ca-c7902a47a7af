// 消息角色枚举在 enums.ts 中定义

export interface ChatMessage {
  id: number; // 消息ID
  conversation_id: number; // 会话ID，关联Conversation表
  user_id: number; // 用户ID，关联User表
  role: 'user' | 'assistant' | 'system'; // 消息角色
  content: string; // 消息内容
  created_at: string; // 消息创建时间
  meta?: any; // 附加信息
}

// 用于创建消息的类型（不包含自增ID和时间戳）
export type CreateChatMessageData = Omit<ChatMessage, 'id' | 'created_at'>;

// 用于更新消息的类型（所有字段可选，除了ID）
export type UpdateChatMessageData = Partial<Omit<ChatMessage, 'id' | 'created_at'>> & {
  id: number;
};
