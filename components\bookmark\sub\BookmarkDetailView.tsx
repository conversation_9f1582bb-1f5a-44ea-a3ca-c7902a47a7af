'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Calendar, User, Book, Link, Tag, FileText, Copy } from 'lucide-react';
import { useBookmarkContext } from '@/context/BookmarkContext';
import { useIconSidebarContext } from '@/context/IconSideBarContext';
import { message } from '@/lib/utils/toast';
import { parseAuthors, parseKeywords, parseTags } from '@/lib/utils/paper-utils';
import type { LocalFavoriteItem } from '@/lib/storage/local-storage';

interface BookmarkDetailViewProps {
  onBack?: () => void;
}

export const BookmarkDetailView: React.FC<BookmarkDetailViewProps> = ({
  onBack
}) => {
  const { selectedItemId, items, setViewMode, updateItem } = useBookmarkContext();
  const { setSelectedKey } = useIconSidebarContext();

  // 编辑状态
  const [isEditingNotes, setIsEditingNotes] = useState(false);
  const [isEditingTags, setIsEditingTags] = useState(false);
  const [userNotes, setUserNotes] = useState('');
  const [userTags, setUserTags] = useState('');

  const selectedItem = selectedItemId ? items.find(item => item.local_id === selectedItemId) : null;

  // 当选中项目变化时重置编辑状态
  useEffect(() => {
    if (selectedItem) {
      setUserNotes(selectedItem.user_notes || '');
      setUserTags(selectedItem.user_tags || '');
      setIsEditingNotes(false);
      setIsEditingTags(false);
    }
  }, [selectedItem]);

  // 处理返回列表视图
  const handleBackToList = () => {
    if (onBack) {
      onBack();
    } else {
      setViewMode('list');
      setSelectedKey('bookmark');
    }
  };

  // 保存备注
  const handleSaveNotes = async () => {
    if (!selectedItem) return;

    try {
      await updateItem(selectedItem.local_id, {
        user_notes: userNotes
      });
      setIsEditingNotes(false);
      message.success('备注已保存');
    } catch (error) {
      console.error('保存备注失败:', error);
      message.error('保存备注失败');
    }
  };

  // 取消编辑备注
  const handleCancelNotes = () => {
    setUserNotes(selectedItem?.user_notes || '');
    setIsEditingNotes(false);
  };

  // 保存标签
  const handleSaveTags = async () => {
    if (!selectedItem) return;

    try {
      await updateItem(selectedItem.local_id, {
        user_tags: userTags
      });
      setIsEditingTags(false);
      message.success('标签已保存');
    } catch (error) {
      console.error('保存标签失败:', error);
      message.error('保存标签失败');
    }
  };

  // 取消编辑标签
  const handleCancelTags = () => {
    setUserTags(selectedItem?.user_tags || '');
    setIsEditingTags(false);
  };

  // 格式化作者
  const formatAuthors = (authors: string) => {
    return parseAuthors(authors);
  };

  // 格式化关键词
  const formatKeywords = (keywords: string) => {
    return parseKeywords(keywords);
  };

  // 格式化标签
  const formatTags = (tags: string) => {
    return parseTags(tags);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  // 复制到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success('已复制到剪贴板');
    } catch (error) {
      message.error('复制失败');
    }
  };

  if (!selectedItem) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center text-gray-500">
          <div className="text-lg mb-2">未找到收藏项目</div>
          <Button onClick={handleBackToList}>返回收藏列表</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* 头部导航 */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              onClick={handleBackToList}
              className="hover:bg-gray-100"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
            <Separator orientation="vertical" className="h-6" />
          </div>
        </div>
      </div>

      {/* 详情内容 */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* 标题卡片 */}
          <Card>
            <CardContent className="p-6 space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">显示标题</p>
                <h3 className="text-xl font-semibold mt-1">
                  {selectedItem.custom_title || selectedItem.translated_title || selectedItem.title}
                </h3>
              </div>

              {selectedItem.custom_title && selectedItem.translated_title && selectedItem.custom_title !== selectedItem.translated_title && (
                <div>
                  <p className="text-sm text-muted-foreground">中文标题</p>
                  <p className="mt-1">{selectedItem.translated_title}</p>
                </div>
              )}

              {selectedItem.title && selectedItem.title !== (selectedItem.custom_title || selectedItem.translated_title) && (
                <div>
                  <p className="text-sm text-muted-foreground">英文标题</p>
                  <p className="mt-1">{selectedItem.title}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 基本信息卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-4 w-4" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {selectedItem.authors && (
                <div>
                  <p className="text-sm text-muted-foreground mb-1">作者</p>
                  <div className="space-y-1">
                    {formatAuthors(selectedItem.authors).map((author, index) => (
                      <Badge key={index} variant="secondary" className="mr-1 mb-1">
                        {author}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {selectedItem.published && (
                <div>
                  <p className="text-sm text-muted-foreground mb-1">发表日期</p>
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4 text-gray-400" />
                    <span>{selectedItem.published}</span>
                  </div>
                </div>
              )}

              {selectedItem.source && (
                <div>
                  <p className="text-sm text-muted-foreground mb-1">来源</p>
                  <div className="flex items-center">
                    <Book className="mr-2 h-4 w-4 text-gray-400" />
                    <span>{selectedItem.source}</span>
                  </div>
                </div>
              )}

              {selectedItem.created_at && (
                <div>
                  <p className="text-sm text-muted-foreground mb-1">收藏时间</p>
                  <div className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4 text-gray-400" />
                    <span>{formatDate(selectedItem.created_at)}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 摘要卡片 */}
          {(selectedItem.summary || selectedItem.translated_summary) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  摘要
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedItem.translated_summary && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">中文摘要</p>
                    <p className="text-gray-700 leading-relaxed">
                      {selectedItem.translated_summary}
                    </p>
                  </div>
                )}

                {selectedItem.summary && selectedItem.summary !== selectedItem.translated_summary && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">英文摘要</p>
                    <p className="text-gray-600 leading-relaxed">
                      {selectedItem.summary}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* 关键词和标签卡片 */}
          {(selectedItem.keywords || selectedItem.user_tags) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Tag className="mr-2 h-4 w-4" />
                  关键词与标签
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedItem.keywords && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">关键词</p>
                    <div className="flex flex-wrap gap-1">
                      {formatKeywords(selectedItem.keywords).map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div>
                  <p className="text-sm text-muted-foreground mb-2">用户标签</p>
                  {isEditingTags ? (
                    <div className="space-y-2">
                      <Input
                        placeholder="输入标签，用逗号分隔"
                        value={userTags}
                        onChange={(e) => setUserTags(e.target.value)}
                      />
                      <div className="flex gap-2">
                        <Button size="sm" onClick={handleSaveTags}>保存</Button>
                        <Button size="sm" variant="outline" onClick={handleCancelTags}>取消</Button>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="mb-2 cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors min-h-[2rem] border border-dashed border-gray-300"
                      onClick={() => setIsEditingTags(true)}
                      title="点击编辑标签"
                    >
                      {formatTags(userTags).length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {formatTags(userTags).map((tag, index) => (
                            <Badge key={index} variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      ) : (
                        <p className="text-muted-foreground italic">暂无标签，点击添加</p>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 文件和链接卡片 */}
          {selectedItem.file_url && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Link className="mr-2 h-4 w-4" />
                  文件链接
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <p className="text-sm text-muted-foreground mb-2">文件 URL</p>
                  <div className="p-3 bg-gray-50 rounded border flex items-center justify-between">
                    <code className="text-sm break-all flex-1 mr-2">
                      {selectedItem.file_url}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(selectedItem.file_url!)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 用户备注卡片 */}
          <Card>
            <CardHeader>
              <CardTitle>用户备注</CardTitle>
            </CardHeader>
            <CardContent>
              {isEditingNotes ? (
                <div className="space-y-2">
                  <Textarea
                    placeholder="添加备注..."
                    value={userNotes}
                    onChange={(e) => setUserNotes(e.target.value)}
                    rows={4}
                  />
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleSaveNotes}>保存</Button>
                    <Button size="sm" variant="outline" onClick={handleCancelNotes}>取消</Button>
                  </div>
                </div>
              ) : (
                <div
                  className="mb-2 cursor-pointer hover:bg-gray-50 p-2 rounded transition-colors min-h-[2rem] border border-dashed border-gray-300"
                  onClick={() => setIsEditingNotes(true)}
                  title="点击编辑备注"
                >
                  {userNotes ? (
                    <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                      {userNotes}
                    </p>
                  ) : (
                    <p className="text-muted-foreground italic">暂无备注，点击添加</p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default BookmarkDetailView;
