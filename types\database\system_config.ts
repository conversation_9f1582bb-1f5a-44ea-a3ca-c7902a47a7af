export interface SystemConfig {
  id: number; // 配置ID
  key: string; // 配置键
  value: string; // 配置值
  description?: string; // 配置描述
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
}

// 用于创建系统配置的类型
export type CreateSystemConfigData = Omit<SystemConfig, 'id' | 'created_at' | 'updated_at'>;

// 用于更新系统配置的类型（所有字段可选，除了ID）
export type UpdateSystemConfigData = Partial<Omit<SystemConfig, 'id' | 'created_at' | 'updated_at'>> & {
  id: number;
};
