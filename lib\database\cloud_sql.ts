import { pool } from './connect';
import type { User, CreateUserData } from '@/types/database/user';
import type { Conversation } from '@/types/database/conversation';
import type { ChatMessage, CreateChatMessageData } from '@/types/database/chat_message';
import type { Paper } from '@/types/database/paper';
import type { FavoriteFolder, CreateFavoriteFolderData } from '@/types/database/favorite_folder';
import type { FavoriteItem, CreateFavoriteItemData, UpdateFavoriteItemData } from '@/types/database/favorite_item';
import type { FavoriteNote, CreateFavoriteNoteData, UpdateFavoriteNoteData } from '@/types/database/favorite_note';
import type { UserActivity, CreateUserActivityData } from '@/types/database/user_activity';
import type { SystemConfig, CreateSystemConfigData, UpdateSystemConfigData } from '@/types/database/system_config';

// ========== User ==========
export async function getUserById(id: number) {
  const [rows] = await pool.query('SELECT * FROM user WHERE id = ?', [id]);
  return (rows as User[])[0];
}

export async function getUserByEmail(email: string) {
  const [rows] = await pool.query('SELECT * FROM user WHERE email = ?', [email]);
  return (rows as User[])[0];
}

export async function createUser(user: CreateUserData) {
  const [result] = await pool.query('INSERT INTO user SET ?', [user]);
  return result;
}

export async function updateUser(id: number, user: Partial<User>) {
  const [result] = await pool.query('UPDATE user SET ? WHERE id = ?', [user, id]);
  return result;
}

export async function deleteUser(id: number) {
  const [result] = await pool.query('DELETE FROM user WHERE id = ?', [id]);
  return result;
}

// ========== Conversation ==========
export async function getConversationById(id: number) {
  const [rows] = await pool.query('SELECT * FROM conversation WHERE id = ?', [id]);
  return (rows as Conversation[])[0];
}

export async function getConversationsByUser(user_id: number) {
  const [rows] = await pool.query('SELECT * FROM conversation WHERE user_id = ?', [user_id]);
  return rows as Conversation[];
}

export async function createConversation(conversation: Conversation) {
  const [result] = await pool.query('INSERT INTO conversation SET ?', [conversation]);
  return result;
}

export async function updateConversation(id: number, conversation: Partial<Conversation>) {
  const [result] = await pool.query('UPDATE conversation SET ? WHERE id = ?', [conversation, id]);
  return result;
}

export async function deleteConversation(id: number) {
  const [result] = await pool.query('DELETE FROM conversation WHERE id = ?', [id]);
  return result;
}

// ========== ChatMessage ==========
export async function getMessagesByConversation(conversation_id: number) {
  const [rows] = await pool.query('SELECT * FROM chat_message WHERE conversation_id = ? ORDER BY created_at ASC', [conversation_id]);
  return rows as ChatMessage[];
}

export async function createChatMessage(message: CreateChatMessageData) {
  const [result] = await pool.query('INSERT INTO chat_message SET ?', [message]);
  return result;
}

export async function deleteChatMessage(id: number) {
  const [result] = await pool.query('DELETE FROM chat_message WHERE id = ?', [id]);
  return result;
}

// ========== Paper ==========
export async function getPaperById(id: number) {
  const [rows] = await pool.query('SELECT * FROM paper WHERE id = ?', [id]);
  return (rows as Paper[])[0];
}

export async function searchPapers(keyword: string) {
  const [rows] = await pool.query('SELECT * FROM paper WHERE MATCH(title, summary, keywords) AGAINST(? IN NATURAL LANGUAGE MODE)', [keyword]);
  return rows as Paper[];
}

export async function createPaper(paper: Paper) {
  const [result] = await pool.query('INSERT INTO paper SET ?', [paper]);
  return result;
}

export async function updatePaper(id: number, paper: Partial<Paper>) {
  const [result] = await pool.query('UPDATE paper SET ? WHERE id = ?', [paper, id]);
  return result;
}

export async function deletePaper(id: number) {
  const [result] = await pool.query('DELETE FROM paper WHERE id = ?', [id]);
  return result;
}

// ========== FavoriteFolder ==========
export async function getFoldersByUser(user_id: number) {
  const [rows] = await pool.query('SELECT * FROM favorite_folder WHERE user_id = ?', [user_id]);
  return rows as FavoriteFolder[];
}

export async function createFavoriteFolder(folder: CreateFavoriteFolderData) {
  const [result] = await pool.query('INSERT INTO favorite_folder SET ?', [folder]);
  return result;
}

export async function updateFavoriteFolder(id: number, folder: Partial<FavoriteFolder>) {
  const [result] = await pool.query('UPDATE favorite_folder SET ? WHERE id = ?', [folder, id]);
  return result;
}

export async function deleteFavoriteFolder(id: number) {
  const [result] = await pool.query('DELETE FROM favorite_folder WHERE id = ?', [id]);
  return result;
}

// ========== FavoriteItem ==========
export async function getFavoriteItemsByUser(user_id: number) {
  const [rows] = await pool.query('SELECT * FROM favorite_item WHERE user_id = ?', [user_id]);
  return rows as FavoriteItem[];
}

export async function getFavoriteItemsByFolder(folder_id: number) {
  const [rows] = await pool.query('SELECT * FROM favorite_item WHERE folder_id = ?', [folder_id]);
  return rows as FavoriteItem[];
}

export async function getFavoriteItemById(id: number) {
  const [rows] = await pool.query('SELECT * FROM favorite_item WHERE id = ?', [id]);
  return (rows as FavoriteItem[])[0];
}

export async function createFavoriteItem(item: CreateFavoriteItemData) {
  const [result] = await pool.query('INSERT INTO favorite_item SET ?', [item]);
  return result;
}

export async function updateFavoriteItem(id: number, item: UpdateFavoriteItemData) {
  const [result] = await pool.query('UPDATE favorite_item SET ? WHERE id = ?', [item, id]);
  return result;
}

export async function deleteFavoriteItem(id: number) {
  const [result] = await pool.query('DELETE FROM favorite_item WHERE id = ?', [id]);
  return result;
}

export async function searchFavoriteItems(user_id: number, keyword: string) {
  const [rows] = await pool.query(
    'SELECT * FROM favorite_item WHERE user_id = ? AND MATCH(title, summary, keywords) AGAINST(? IN NATURAL LANGUAGE MODE)',
    [user_id, keyword]
  );
  return rows as FavoriteItem[];
}

// ========== FavoriteNote ==========
export async function getNotesByFavoriteItem(favorite_item_id: number) {
  const [rows] = await pool.query('SELECT * FROM favorite_note WHERE favorite_item_id = ?', [favorite_item_id]);
  return rows as FavoriteNote[];
}

export async function getNotesByUser(user_id: number) {
  const [rows] = await pool.query('SELECT * FROM favorite_note WHERE user_id = ?', [user_id]);
  return rows as FavoriteNote[];
}

export async function getFavoriteNoteById(id: number) {
  const [rows] = await pool.query('SELECT * FROM favorite_note WHERE id = ?', [id]);
  return (rows as FavoriteNote[])[0];
}

export async function createFavoriteNote(note: CreateFavoriteNoteData) {
  const [result] = await pool.query('INSERT INTO favorite_note SET ?', [note]);
  return result;
}

export async function updateFavoriteNote(id: number, note: UpdateFavoriteNoteData) {
  const [result] = await pool.query('UPDATE favorite_note SET ? WHERE id = ?', [note, id]);
  return result;
}

export async function deleteFavoriteNote(id: number) {
  const [result] = await pool.query('DELETE FROM favorite_note WHERE id = ?', [id]);
  return result;
}

// ========== UserActivity ==========
export async function getActivitiesByUser(user_id: number, limit: number = 50) {
  const [rows] = await pool.query('SELECT * FROM user_activity WHERE user_id = ? ORDER BY created_at DESC LIMIT ?', [user_id, limit]);
  return rows as UserActivity[];
}

export async function createUserActivity(activity: CreateUserActivityData) {
  const [result] = await pool.query('INSERT INTO user_activity SET ?', [activity]);
  return result;
}

export async function deleteUserActivity(id: number) {
  const [result] = await pool.query('DELETE FROM user_activity WHERE id = ?', [id]);
  return result;
}

// ========== SystemConfig ==========
export async function getSystemConfig(config_type: string, config_key: string) {
  const [rows] = await pool.query('SELECT * FROM system_config WHERE config_type = ? AND config_key = ?', [config_type, config_key]);
  return (rows as SystemConfig[])[0];
}

export async function getAllSystemConfigs() {
  const [rows] = await pool.query('SELECT * FROM system_config ORDER BY config_type, config_key');
  return rows as SystemConfig[];
}

export async function getSystemConfigsByType(config_type: string) {
  const [rows] = await pool.query('SELECT * FROM system_config WHERE config_type = ? ORDER BY config_key', [config_type]);
  return rows as SystemConfig[];
}

export async function createSystemConfig(config: CreateSystemConfigData) {
  const [result] = await pool.query('INSERT INTO system_config SET ?', [config]);
  return result;
}

export async function updateSystemConfig(id: number, config: UpdateSystemConfigData) {
  const [result] = await pool.query('UPDATE system_config SET ? WHERE id = ?', [config, id]);
  return result;
}

export async function deleteSystemConfig(id: number) {
  const [result] = await pool.query('DELETE FROM system_config WHERE id = ?', [id]);
  return result;
}