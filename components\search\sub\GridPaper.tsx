"use client";

import React, { useState, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { TooltipS } from '@/components/ui/tooltip-simple';
import { useSearchContext } from '@/context/SearchContext';
import { useBookmarkContext } from '@/context/BookmarkContext';
import { useIconSidebarContext } from '@/context/IconSideBarContext';
import { MdPictureAsPdf } from "react-icons/md";
import { Bookmark, BookmarkCheck, Loader2 } from "lucide-react";
import type { PaperWithExtras as Paper } from '@/types/database/paper';
import { ItemType, SourceType } from '@/types/database';
import { normalizeAuthors, normalizeKeywords } from '@/lib/utils/paper-utils';
import { message } from '@/lib/utils/toast';

export default function GridPaper() {
  const { papers, loading } = useSearchContext();
  const { createItem, deleteItem, items } = useBookmarkContext();
  const {
    leftVisible, setLeftVisible,
  } = useIconSidebarContext();

  const [bookmarkingItems, setBookmarkingItems] = useState<Set<string>>(new Set());

  // 基于 items 数组实时计算收藏状态，无需独立状态管理
  const getBookmarkStatus = useCallback((sourceId: string, sourceType: string) => {
    return items.some(item =>
      item.source_id === sourceId &&
      item.source_type === sourceType &&
      item.status !== 'deleted'
    );
  }, [items]);

  // 处理收藏操作（离线优先）
  const handleBookmark = async (paper: Paper) => {
    const sourceId = paper.pdf_url || paper.title;

    // 如果已经收藏，则取消收藏
    if (getBookmarkStatus(sourceId, SourceType.ARXIV)) {
      await handleUnbookmark(sourceId);
      return;
    }

    setBookmarkingItems(prev => new Set(prev).add(sourceId));

    try {
      // 如果没有中文标题，先尝试翻译
      let finalTranslatedTitle = paper.translated_title;
      if (!finalTranslatedTitle && paper.title) {
        try {
          const translationResponse = await fetch('/api/translate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              query: [paper.title]
            }),
          });

          if (translationResponse.ok) {
            const data = await translationResponse.json();
            if (data && data.translation) {
              const translations = Array.isArray(data.translation)
                ? data.translation
                : JSON.parse(data.translation);
              if (translations.length > 0) {
                finalTranslatedTitle = translations[0];
              }
            }
          }
        } catch (translationError) {
          console.warn('收藏时翻译标题失败:', translationError);
          // 翻译失败不影响收藏，继续使用原标题
        }
      }

      await createItem({
        item_type: ItemType.PAPER,
        source_id: sourceId,
        source_type: SourceType.ARXIV,
        title: paper.title,
        authors: normalizeAuthors(paper.authors),
        summary: paper.summary,
        keywords: normalizeKeywords(paper.keywords),
        published: paper.published,
        source: paper.source,
        doi: paper.doi,
        file_url: paper.pdf_url,
        custom_title: finalTranslatedTitle, // 使用中文标题作为自定义标题
        translated_title: finalTranslatedTitle,
        translated_summary: paper.translated_summary,
      });

      // 不需要手动更新状态，BookmarkContext 会自动更新 items 数组

      // 显示收藏侧栏但不切换主面板
      if (!leftVisible) {
        setLeftVisible(true);
      }
    } catch (error) {
      console.error('收藏失败:', error);
    } finally {
      setBookmarkingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(sourceId);
        return newSet;
      });
    }
  };

  // 处理取消收藏操作
  const handleUnbookmark = async (sourceId: string) => {
    setBookmarkingItems(prev => new Set(prev).add(sourceId));

    try {
      // 找到对应的收藏项目并删除
      const bookmarkedItem = items.find(item =>
        item.source_id === sourceId && item.status !== 'deleted'
      );

      if (bookmarkedItem) {
        await deleteItem(bookmarkedItem.local_id);
        // 不需要手动更新状态，BookmarkContext 会自动更新 items 数组
      }
    } catch (error) {
      console.error('取消收藏失败:', error);
      message.error('取消收藏失败');
    } finally {
      setBookmarkingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(sourceId);
        return newSet;
      });
    }
  };

  if (loading) return (
    <div className="flex justify-center items-center w-full mt-10">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>
  );

  return (
    <div
      className={`grid grid-cols-1 md:grid-cols-3 gap-4 mt-6
        max-w-[1066px]
      `}
    >
      {papers.map((paper, idx) => {
        const sourceId = paper.pdf_url || paper.title;
        const isBookmarked = getBookmarkStatus(sourceId, SourceType.ARXIV);
        const isBookmarking = bookmarkingItems.has(sourceId);

        return (
          <Card key={paper.pdf_url || idx} className="shadow-md transition-shadow duration-200 hover:shadow-xl bg-white dark:bg-neutral-900">
            <div className="p-4">
              <div className="flex justify-between items-start mb-2">
                <TooltipS content={paper.translated_title || paper.title} side="top">
                  <h3 className="font-bold text-sm line-clamp-2 flex-1 mr-2">
                    {`${idx + 1}. ${paper.translated_title || paper.title}`}
                  </h3>
                </TooltipS>
                <div className="flex gap-1 items-center flex-shrink-0">
                  {paper.pdf_url && (
                    <a href={paper.pdf_url} target="_blank" rel="noopener noreferrer">
                      <TooltipS content="查看 PDF" side="top">
                        <MdPictureAsPdf className="text-xl text-gray-500 hover:text-gray-700" size={18} />
                      </TooltipS>
                    </a>
                  )}
                  <TooltipS content={isBookmarked ? '取消收藏' : isBookmarking ? '收藏中...' : '收藏'} side="top">
                    {isBookmarked ? (
                      <BookmarkCheck
                        className="text-xl text-blue-500 cursor-pointer hover:text-blue-600"
                        size={18}
                        onClick={() => handleBookmark(paper)}
                      />
                    ) : (
                      <Bookmark
                        className={`text-xl cursor-pointer transition-colors ${
                          isBookmarking
                            ? 'text-blue-400 animate-pulse'
                            : 'text-gray-500 hover:text-blue-500'
                        }`}
                        size={18}
                        onClick={() => handleBookmark(paper)}
                      />
                    )}
                  </TooltipS>
                </div>
              </div>
              <div className="text-xs text-gray-500 mb-2">
                {(paper.published || '').slice(0, 10)} - {paper.source}
              </div>
              <div className="text-sm line-clamp-3">{paper.summary}</div>
            </div>
          </Card>
        );
      })}
    </div>
  );
}
