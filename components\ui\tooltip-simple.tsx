"use client";

import * as React from "react";
import { Tooltip as BaseTooltip, TooltipTrigger, TooltipContent } from "./tooltip";

type TooltipSProps = {
  content: React.ReactNode;
  side?: "top" | "bottom" | "left" | "right";
  asChild?: boolean; // 默认作为子元素触发
  children: React.ReactNode;
};

export function TooltipS({ content, side = "top", asChild = true, children }: TooltipSProps) {
  return (
    <BaseTooltip>
      <TooltipTrigger asChild={asChild}>{children as React.ReactElement}</TooltipTrigger>
      <TooltipContent side={side}>{content}</TooltipContent>
    </BaseTooltip>
  );
}

