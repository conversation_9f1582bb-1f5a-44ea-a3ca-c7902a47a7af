'use client';

import { useState, useCallback, useEffect } from 'react';
import {
  checkStorageQuota,
  generateQuotaWarning,
  type StorageQuotaStatus
} from '@/lib/utils/storage-quota';
import { localBookmarkStorage, type LocalFavoriteFolder, type LocalFavoriteItem } from '@/lib/storage/local-storage';
import { message } from '@/lib/utils/toast';
import type { User } from '@/types/database';

/**
 * 书签存储相关的 Hook
 * 处理存储策略选择、配额检查、数据刷新等功能
 * 登录用户和未登录用户使用完全隔离的存储空间
 */
export const useBookmarkStorage = (user: User | null, defaultFolderId: string | null) => {
  const [folders, setFolders] = useState<LocalFavoriteFolder[]>([]);
  const [items, setItems] = useState<LocalFavoriteItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [storageQuota, setStorageQuota] = useState<StorageQuotaStatus | null>(null);

  // 判断是否为登录用户（使用独立的用户ID存储空间）
  const isLoggedInUser = useCallback(() => {
    return user !== null && user.id > 0;
  }, [user]);

  // 检查配额警告
  const checkQuotaWarning = useCallback(() => {
    if (!user) return null;
    return generateQuotaWarning(user);
  }, [user]);

  // 更新存储配额状态
  const updateStorageQuota = useCallback(() => {
    if (!user) {
      setStorageQuota(null);
      return;
    }

    const quotaStatus = checkStorageQuota(user);
    setStorageQuota(quotaStatus);
  }, [user]);

  // 刷新数据（根据用户状态选择存储策略）
  const refreshData = useCallback(async (preserveDefaultFolder = false) => {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined') {
      return;
    }

    setLoading(true);
    try {
      let foldersData: LocalFavoriteFolder[];
      let itemsData: LocalFavoriteItem[];

      if (isLoggedInUser()) {
        // 登录用户：使用独立的用户ID存储空间
        const userId = user?.id || 0;
        [foldersData, itemsData] = await Promise.all([
          localBookmarkStorage.getFoldersByUser(userId),
          localBookmarkStorage.getItemsByUser(userId),
        ]);
      } else {
        // 未登录用户：使用独立的本地存储空间（ID 0）
        const userId = 0; // 未登录用户统一使用ID 0
        [foldersData, itemsData] = await Promise.all([
          localBookmarkStorage.getFoldersByUser(userId),
          localBookmarkStorage.getItemsByUser(userId),
        ]);
      }

      // 更新状态
      setFolders(foldersData);
      setItems(itemsData);

      // 验证默认文件夹是否仍然存在
      if (defaultFolderId && !preserveDefaultFolder) {
        const existingDefault = foldersData.find(f => f.local_id === defaultFolderId);
        if (!existingDefault) {
          // 这里需要通过回调来更新默认文件夹ID
          // setDefaultFolderId(null);
        }
      }
    } catch (error) {
      console.error('刷新收藏数据失败:', error);
      message.error('加载收藏数据失败');
    } finally {
      setLoading(false);
    }
  }, [user?.id, defaultFolderId, isLoggedInUser]);

  // 用户变化时刷新数据和配额状态
  useEffect(() => {
    refreshData(false); // 初始化时允许设置默认文件夹，支持未登录用户
    updateStorageQuota();
  }, [refreshData, updateStorageQuota]);

  return {
    folders,
    items,
    loading,
    storageQuota,
    checkQuotaWarning,
    updateStorageQuota,
    refreshData,
    setFolders,
    setItems,
  };
};
