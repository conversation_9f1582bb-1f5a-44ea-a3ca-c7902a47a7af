/**
 * 本地存储管理工具类
 * 支持IndexedDB存储收藏夹和收藏项目数据
 * 登录用户和未登录用户数据完全隔离，不进行任何合并或同步
 */

import type { FavoriteFolder, CreateFavoriteFolderData } from '@/types/database/favorite_folder';
import type { FavoriteItem, CreateFavoriteItemData } from '@/types/database/favorite_item';
import type { FavoriteNote, CreateFavoriteNoteData } from '@/types/database/favorite_note';

// 本地存储的数据结构，登录用户和未登录用户完全隔离
export interface LocalFavoriteFolder extends Omit<FavoriteFolder, 'id' | 'parent_id'> {
  local_id: string; // 本地唯一ID
  parent_id?: string; // 本地父文件夹ID
  status: 'active' | 'deleted'; // 数据状态（仅用于软删除）
  last_modified: number; // 最后修改时间戳
}

export interface LocalFavoriteItem extends Omit<FavoriteItem, 'id' | 'folder_id'> {
  local_id: string;
  folder_local_id: string; // 关联本地文件夹ID
  status: 'active' | 'deleted'; // 数据状态（仅用于软删除）
  last_modified: number;
}

export interface LocalFavoriteNote extends Omit<FavoriteNote, 'id' | 'favorite_item_id'> {
  local_id: string;
  item_local_id: string; // 关联本地收藏项目ID
  status: 'active' | 'deleted'; // 数据状态（仅用于软删除）
  last_modified: number;
}

// 数据库配置
const DB_NAME = 'AxsightBookmarks';
const DB_VERSION = 1;
const FOLDERS_STORE = 'folders';
const ITEMS_STORE = 'items';
const NOTES_STORE = 'notes';

class LocalBookmarkStorage {
  private db: IDBDatabase | null = null;
  private dbPromise: Promise<IDBDatabase> | null = null;

  constructor() {
    // 只在浏览器环境中初始化数据库
    if (this.isBrowserEnvironment()) {
      this.initDB();
    }
  }

  // 检查是否在浏览器环境中
  private isBrowserEnvironment(): boolean {
    return typeof window !== 'undefined' && typeof indexedDB !== 'undefined';
  }

  // 确保在浏览器环境中运行
  private ensureBrowserEnvironment(): void {
    if (!this.isBrowserEnvironment()) {
      throw new Error('This operation requires a browser environment with IndexedDB support');
    }
  }

  // 初始化数据库
  private async initDB(): Promise<IDBDatabase> {
    if (this.dbPromise) {
      return this.dbPromise;
    }

    // 检查是否在浏览器环境中
    this.ensureBrowserEnvironment();

    this.dbPromise = new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(request.result);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        // 创建收藏夹存储
        if (!db.objectStoreNames.contains(FOLDERS_STORE)) {
          const foldersStore = db.createObjectStore(FOLDERS_STORE, { keyPath: 'local_id' });
          foldersStore.createIndex('user_id', 'user_id', { unique: false });
          foldersStore.createIndex('parent_id', 'parent_id', { unique: false });
          foldersStore.createIndex('status', 'status', { unique: false });
        }

        // 创建收藏项目存储
        if (!db.objectStoreNames.contains(ITEMS_STORE)) {
          const itemsStore = db.createObjectStore(ITEMS_STORE, { keyPath: 'local_id' });
          itemsStore.createIndex('user_id', 'user_id', { unique: false });
          itemsStore.createIndex('folder_local_id', 'folder_local_id', { unique: false });
          itemsStore.createIndex('status', 'status', { unique: false });
          itemsStore.createIndex('source_id_type', ['source_id', 'source_type'], { unique: false });
        }

        // 创建笔记存储
        if (!db.objectStoreNames.contains(NOTES_STORE)) {
          const notesStore = db.createObjectStore(NOTES_STORE, { keyPath: 'local_id' });
          notesStore.createIndex('user_id', 'user_id', { unique: false });
          notesStore.createIndex('item_local_id', 'item_local_id', { unique: false });
          notesStore.createIndex('status', 'status', { unique: false });
        }
      };
    });

    return this.dbPromise;
  }

  // 生成本地唯一ID
  private generateLocalId(): string {
    return `local_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // 获取当前时间戳
  private getCurrentTimestamp(): number {
    return Date.now();
  }

  // ========== 收藏夹操作 ==========

  // 创建收藏夹
  async createFolder(folderData: CreateFavoriteFolderData & { parent_local_id?: string }): Promise<LocalFavoriteFolder> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([FOLDERS_STORE], 'readwrite');
    const store = transaction.objectStore(FOLDERS_STORE);

    const { parent_local_id, ...restData } = folderData;
    const localFolder: LocalFavoriteFolder = {
      ...restData,
      local_id: this.generateLocalId(),
      parent_id: parent_local_id,
      status: 'active',
      last_modified: this.getCurrentTimestamp(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    await new Promise((resolve, reject) => {
      const request = store.add(localFolder);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });

    return localFolder;
  }

  // 获取用户的所有收藏夹
  async getFoldersByUser(userId: number): Promise<LocalFavoriteFolder[]> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([FOLDERS_STORE], 'readonly');
    const store = transaction.objectStore(FOLDERS_STORE);
    const index = store.index('user_id');

    return new Promise((resolve, reject) => {
      const request = index.getAll(userId);
      request.onsuccess = () => {
        const folders = request.result.filter(folder => folder.status !== 'deleted');
        resolve(folders);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 更新收藏夹
  async updateFolder(localId: string, updates: Partial<LocalFavoriteFolder>): Promise<void> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([FOLDERS_STORE], 'readwrite');
    const store = transaction.objectStore(FOLDERS_STORE);

    // 先获取现有数据
    const getRequest = store.get(localId);
    await new Promise((resolve, reject) => {
      getRequest.onsuccess = () => resolve(getRequest.result);
      getRequest.onerror = () => reject(getRequest.error);
    });

    if (!getRequest.result) {
      throw new Error('Folder not found');
    }

    const updatedFolder: LocalFavoriteFolder = {
      ...getRequest.result,
      ...updates,
      last_modified: this.getCurrentTimestamp(),
      updated_at: new Date().toISOString(),
      // 如果 updates 中包含 status，使用它；否则保持原状态
      status: updates.status || getRequest.result.status,
    };

    await new Promise((resolve, reject) => {
      const request = store.put(updatedFolder);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // 删除收藏夹（软删除）
  async deleteFolder(localId: string): Promise<void> {
    await this.updateFolder(localId, {
      status: 'deleted',
    });
  }

  // ========== 收藏项目操作 ==========

  // 创建收藏项目
  async createItem(itemData: Omit<CreateFavoriteItemData, 'folder_id'> & { folder_local_id: string }): Promise<LocalFavoriteItem> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([ITEMS_STORE], 'readwrite');
    const store = transaction.objectStore(ITEMS_STORE);

    const { folder_local_id, ...restData } = itemData;
    const localItem: LocalFavoriteItem = {
      ...restData,
      local_id: this.generateLocalId(),
      folder_local_id,
      status: 'active',
      last_modified: this.getCurrentTimestamp(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    await new Promise((resolve, reject) => {
      const request = store.add(localItem);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });

    return localItem;
  }

  // 获取文件夹中的收藏项目
  async getItemsByFolder(folderLocalId: string): Promise<LocalFavoriteItem[]> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([ITEMS_STORE], 'readonly');
    const store = transaction.objectStore(ITEMS_STORE);
    const index = store.index('folder_local_id');

    return new Promise((resolve, reject) => {
      const request = index.getAll(folderLocalId);
      request.onsuccess = () => {
        const items = request.result.filter(item => item.status !== 'deleted');
        resolve(items);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 获取子文件夹
  async getSubFolders(parentLocalId: string): Promise<LocalFavoriteFolder[]> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([FOLDERS_STORE], 'readonly');
    const store = transaction.objectStore(FOLDERS_STORE);

    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => {
        const allFolders = request.result.filter(folder => folder.status !== 'deleted');
        const subFolders = allFolders.filter(folder => folder.parent_id === parentLocalId);
        resolve(subFolders);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 获取用户的所有收藏项目
  async getItemsByUser(userId: number): Promise<LocalFavoriteItem[]> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([ITEMS_STORE], 'readonly');
    const store = transaction.objectStore(ITEMS_STORE);
    const index = store.index('user_id');

    return new Promise((resolve, reject) => {
      const request = index.getAll(userId);
      request.onsuccess = () => {
        const items = request.result.filter(item => item.status !== 'deleted');
        resolve(items);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 更新收藏项目
  async updateItem(localId: string, updates: Partial<LocalFavoriteItem>): Promise<void> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([ITEMS_STORE], 'readwrite');
    const store = transaction.objectStore(ITEMS_STORE);

    const getRequest = store.get(localId);
    await new Promise((resolve, reject) => {
      getRequest.onsuccess = () => resolve(getRequest.result);
      getRequest.onerror = () => reject(getRequest.error);
    });

    if (!getRequest.result) {
      throw new Error('Item not found');
    }

    const updatedItem: LocalFavoriteItem = {
      ...getRequest.result,
      ...updates,
      last_modified: this.getCurrentTimestamp(),
      updated_at: new Date().toISOString(),
      // 如果 updates 中包含 status，使用它；否则保持原状态
      status: updates.status || getRequest.result.status,
    };

    await new Promise((resolve, reject) => {
      const request = store.put(updatedItem);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // 删除收藏项目（软删除）
  async deleteItem(localId: string): Promise<void> {
    await this.updateItem(localId, {
      status: 'deleted',
    });
  }

  // 检查是否已收藏（根据source_id和source_type）
  async isItemBookmarked(userId: number, sourceId: string, sourceType: string): Promise<boolean> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([ITEMS_STORE], 'readonly');
    const store = transaction.objectStore(ITEMS_STORE);
    const index = store.index('source_id_type');

    return new Promise((resolve, reject) => {
      const request = index.getAll([sourceId, sourceType]);
      request.onsuccess = () => {
        const items = request.result.filter(item =>
          item.user_id === userId && item.status !== 'deleted'
        );
        resolve(items.length > 0);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // ========== 笔记操作 ==========

  // 创建笔记
  async createNote(noteData: Omit<CreateFavoriteNoteData, 'favorite_item_id'> & { item_local_id: string }): Promise<LocalFavoriteNote> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([NOTES_STORE], 'readwrite');
    const store = transaction.objectStore(NOTES_STORE);

    const { item_local_id, ...restData } = noteData;
    const localNote: LocalFavoriteNote = {
      ...restData,
      local_id: this.generateLocalId(),
      item_local_id,
      status: 'active',
      last_modified: this.getCurrentTimestamp(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    await new Promise((resolve, reject) => {
      const request = store.add(localNote);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });

    return localNote;
  }

  // 获取收藏项目的笔记
  async getNoteByItem(itemLocalId: string): Promise<LocalFavoriteNote | null> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([NOTES_STORE], 'readonly');
    const store = transaction.objectStore(NOTES_STORE);
    const index = store.index('item_local_id');

    return new Promise((resolve, reject) => {
      const request = index.getAll(itemLocalId);
      request.onsuccess = () => {
        const notes = request.result.filter(note => note.status !== 'deleted');
        resolve(notes.length > 0 ? notes[0] : null);
      };
      request.onerror = () => reject(request.error);
    });
  }

  // 更新笔记
  async updateNote(localId: string, updates: Partial<LocalFavoriteNote>): Promise<void> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const transaction = db.transaction([NOTES_STORE], 'readwrite');
    const store = transaction.objectStore(NOTES_STORE);

    const getRequest = store.get(localId);
    await new Promise((resolve, reject) => {
      getRequest.onsuccess = () => resolve(getRequest.result);
      getRequest.onerror = () => reject(getRequest.error);
    });

    if (!getRequest.result) {
      throw new Error('Note not found');
    }

    const updatedNote: LocalFavoriteNote = {
      ...getRequest.result,
      ...updates,
      last_modified: this.getCurrentTimestamp(),
      updated_at: new Date().toISOString(),
      // 如果 updates 中包含 status，使用它；否则保持原状态
      status: updates.status || getRequest.result.status,
    };

    await new Promise((resolve, reject) => {
      const request = store.put(updatedNote);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // 删除笔记（软删除）
  async deleteNote(localId: string): Promise<void> {
    await this.updateNote(localId, {
      status: 'deleted',
    });
  }

  // ========== 数据清理操作 ==========

  // 清理已删除的数据
  async cleanupDeletedData(): Promise<void> {
    this.ensureBrowserEnvironment();
    const db = await this.initDB();
    const stores = [FOLDERS_STORE, ITEMS_STORE, NOTES_STORE];
    
    for (const storeName of stores) {
      const transaction = db.transaction([storeName], 'readwrite');
      const store = transaction.objectStore(storeName);
      const index = store.index('status');

      await new Promise((resolve, reject) => {
        const request = index.getAll('deleted');
        request.onsuccess = () => {
          const deletedItems = request.result;
          const deletePromises = deletedItems.map(item => {
            return new Promise((deleteResolve, deleteReject) => {
              const deleteRequest = store.delete(item.local_id);
              deleteRequest.onsuccess = () => deleteResolve(deleteRequest.result);
              deleteRequest.onerror = () => deleteReject(deleteRequest.error);
            });
          });

          Promise.all(deletePromises).then(() => resolve(undefined)).catch(reject);
        };
        request.onerror = () => reject(request.error);
      });
    }
  }
}

// 创建一个安全的包装器类，在服务端渲染时提供空实现
class SafeLocalBookmarkStorage {
  private storage: LocalBookmarkStorage | null = null;

  constructor() {
    if (typeof window !== 'undefined') {
      this.storage = new LocalBookmarkStorage();
    }
  }

  private ensureStorage(): LocalBookmarkStorage {
    if (!this.storage) {
      throw new Error('本地存储在服务端环境中不可用');
    }
    return this.storage;
  }

  async createFolder(folderData: CreateFavoriteFolderData & { parent_local_id?: string }): Promise<LocalFavoriteFolder> {
    return this.ensureStorage().createFolder(folderData);
  }

  async getFoldersByUser(userId: number): Promise<LocalFavoriteFolder[]> {
    return this.ensureStorage().getFoldersByUser(userId);
  }

  async updateFolder(localId: string, updates: Partial<LocalFavoriteFolder>): Promise<void> {
    return this.ensureStorage().updateFolder(localId, updates);
  }

  async deleteFolder(localId: string): Promise<void> {
    return this.ensureStorage().deleteFolder(localId);
  }

  async createItem(itemData: Omit<CreateFavoriteItemData, 'folder_id'> & { folder_local_id: string }): Promise<LocalFavoriteItem> {
    return this.ensureStorage().createItem(itemData);
  }

  async getItemsByFolder(folderLocalId: string): Promise<LocalFavoriteItem[]> {
    return this.ensureStorage().getItemsByFolder(folderLocalId);
  }

  async getSubFolders(parentLocalId: string): Promise<LocalFavoriteFolder[]> {
    return this.ensureStorage().getSubFolders(parentLocalId);
  }

  async getItemsByUser(userId: number): Promise<LocalFavoriteItem[]> {
    return this.ensureStorage().getItemsByUser(userId);
  }

  async updateItem(localId: string, updates: Partial<LocalFavoriteItem>): Promise<void> {
    return this.ensureStorage().updateItem(localId, updates);
  }

  async deleteItem(localId: string): Promise<void> {
    return this.ensureStorage().deleteItem(localId);
  }

  async isItemBookmarked(userId: number, sourceId: string, sourceType: string): Promise<boolean> {
    return this.ensureStorage().isItemBookmarked(userId, sourceId, sourceType);
  }

  async createNote(noteData: Omit<CreateFavoriteNoteData, 'favorite_item_id'> & { item_local_id: string }): Promise<LocalFavoriteNote> {
    return this.ensureStorage().createNote(noteData);
  }

  async getNoteByItem(itemLocalId: string): Promise<LocalFavoriteNote | null> {
    return this.ensureStorage().getNoteByItem(itemLocalId);
  }

  async updateNote(localId: string, updates: Partial<LocalFavoriteNote>): Promise<void> {
    return this.ensureStorage().updateNote(localId, updates);
  }

  async deleteNote(localId: string): Promise<void> {
    return this.ensureStorage().deleteNote(localId);
  }



  async cleanupDeletedData(): Promise<void> {
    return this.ensureStorage().cleanupDeletedData();
  }
}

// 导出单例实例
export const localBookmarkStorage = new SafeLocalBookmarkStorage();
export default localBookmarkStorage;
