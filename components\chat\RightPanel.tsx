import React, { useRef, useState, useEffect } from "react";
import { Bo<PERSON> } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { message } from '@/lib/utils/toast';
import { models, DEFAULT_MODEL } from '@/lib/llm/models';
import { MessageBubble } from './MessageBubble';
import { ChatInput } from './ChatInput';

interface Message {
  role: "user" | "assistant" | "system";
  content: string;
  timestamp?: Date;
  id?: string;
}

const Chat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);
  const controllerRef = useRef<AbortController | null>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [selectedModel, setSelectedModel] = useState(DEFAULT_MODEL.id);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 不再需要初始欢迎消息，使用空状态显示

  // 自动滚动到底部
  useEffect(() => {
    if (messagesEndRef.current) {
      // 使用setTimeout确保DOM更新后再滚动
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 100);
    }
  }, [messages, isStreaming]);

  // 发送消息
  const sendMessage = async (content: string) => {
    // 去除末尾空格和所有连续回车
    content = content.replace(/[\s\r\n]+$/g, '');
    if (!content.trim() && !isStreaming) return;

    // 如果正在流式响应，则中断
    if (isStreaming) {
      controllerRef.current?.abort();
      controllerRef.current = null;
      setIsStreaming(false);
      setLoading(false);
      return;
    }

    setLoading(true);
    setIsStreaming(true);

    const userMessage: Message = {
      role: "user",
      content,
      timestamp: new Date(),
      id: `user-${Date.now()}`
    };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");

    controllerRef.current?.abort();
    controllerRef.current = new AbortController();
    const { signal } = controllerRef.current;

    try {
      const response = await fetch("/api/stream-chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          prompt: content,
          history_messages: messages,
          model: selectedModel,
          system_prompt: '你是一个AI助手，请根据用户的问题给出回答，所有回答都要用markdown格式，公式用 $...$ 或者 $$...$$ 渲染，千万不要用 [] 包裹。',
          temperature: 0.8,
        }),
        signal,
      });
      if (!response.body) throw new Error("无响应体");
      const reader = response.body.getReader();
      let aiMsg = "";
      const aiMessageId = `assistant-${Date.now()}`;

      setMessages((prev) => [...prev, {
        role: "assistant",
        content: "",
        timestamp: new Date(),
        id: aiMessageId
      }]);
      while (true) {
        if (signal.aborted) break;
        const { done, value } = await reader.read();
        if (done) break;
        const text = new TextDecoder().decode(value);
        // 逐行处理 data: ... 格式
        const lines = text.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (!data || data === '[DONE]') continue;
            try {
              const parsed = JSON.parse(data);
              if (parsed.content) {
                aiMsg += parsed.content;
                setMessages((prev) => {
                  const newMessages = [...prev];
                  const lastIndex = newMessages.length - 1;
                  if (lastIndex >= 0) {
                    newMessages[lastIndex] = {
                      ...newMessages[lastIndex],
                      content: aiMsg,
                    };
                  }
                  return newMessages;
                });
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } catch (err: any) {
      if (err.name !== 'AbortError') {
        setMessages((prev) => [
          ...prev,
          {
            role: "assistant",
            content: "抱歉，发生了错误，请稍后重试。",
            timestamp: new Date(),
            id: `error-${Date.now()}`
          },
        ]);
      }
    } finally {
      setIsStreaming(false);
      setLoading(false);
      controllerRef.current = null;
    }
  };

  const handleCopy = (content: string) => {
    if (!content) return message.success('内容为空');
    navigator.clipboard.writeText(content)
      .then(() => {
        message.success('复制成功');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  };

  const handleRegenerate = () => {
    // 重新生成最后一条AI消息
    if (messages.length > 0) {
      const lastUserMessage = [...messages].reverse().find(msg => msg.role === 'user');
      if (lastUserMessage) {
        // 移除最后一条AI消息
        setMessages(prev => {
          const newMessages = [...prev];
          const lastIndex = newMessages.length - 1;
          if (lastIndex >= 0 && newMessages[lastIndex].role === 'assistant') {
            newMessages.pop();
          }
          return newMessages;
        });
        // 重新发送
        sendMessage(lastUserMessage.content);
      }
    }
  };

  const handleFeedback = (messageId: string, type: 'like' | 'dislike') => {
    // 这里可以添加反馈处理逻辑，比如发送到后端
    console.log(`Message ${messageId} received ${type} feedback`);
  };

  const handleStop = () => {
    if (isStreaming) {
      controllerRef.current?.abort();
      controllerRef.current = null;
      setIsStreaming(false);
      setLoading(false);
    }
  };

  return (
    <div className="h-full flex flex-col bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800 overflow-hidden relative">
      {/* 悬浮模型选择按钮 */}
      <div className="absolute top-4 left-4 z-10">
        <Select value={selectedModel} onValueChange={setSelectedModel} disabled={loading || isStreaming}>
          <SelectTrigger className="w-auto min-w-[120px] rounded-xl shadow-sm bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm hover:bg-white dark:hover:bg-gray-800 text-sm h-9 border-gray-200 dark:border-gray-600">
            <SelectValue placeholder="选择模型">
              {models.find(model => model.id === selectedModel)?.name || "选择模型"}
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="max-w-xs">
            {models.map((model) => (
              <SelectItem key={model.id} value={model.id}>
                <div className="flex flex-col gap-1">
                  <div className="font-medium text-sm">{model.name}</div>
                  <div className="text-xs text-muted-foreground line-clamp-2">{model.description}</div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      {/* 对话历史 */}
      <ScrollArea className="flex-1 overflow-hidden relative">
        <div className="w-full px-3 pt-14 pb-40">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Bot className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  开始对话
                </h3>
                <p className="text-gray-500 dark:text-gray-400 max-w-md">
                  我是您的AI助手，可以帮助您解答问题、分析文档、总结内容等。请输入您的问题开始对话。
                </p>
              </div>
            </div>
          ) : (
            <>
              {messages.map((msg, idx) => (
                <MessageBubble
                  key={msg.id || idx}
                  message={msg}
                  isStreaming={isStreaming && idx === messages.length - 1 && msg.role === 'assistant'}
                  onCopy={handleCopy}
                  onRegenerate={idx === messages.length - 1 && msg.role === 'assistant' ? handleRegenerate : undefined}
                  onFeedback={handleFeedback}
                />
              ))}
              <div ref={messagesEndRef} />
            </>
          )}
        </div>
        {/* 底部渐变遮罩 */}
        <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-gray-50/90 via-gray-50/60 to-transparent dark:from-gray-900/90 dark:via-gray-900/60 pointer-events-none" />
      </ScrollArea>

      {/* 悬浮输入框 */}
      <ChatInput
        value={input}
        onChange={setInput}
        onSend={sendMessage}
        onStop={handleStop}
        disabled={loading}
        isStreaming={isStreaming}
        placeholder="输入消息... (Shift + Enter 换行)"
        maxLength={2000}
      />
    </div>
  );
};

export default Chat;

