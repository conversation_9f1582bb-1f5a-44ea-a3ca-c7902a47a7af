'use client';

import React, { useState } from 'react';
import {
  ContextMenuContent,
  ContextMenuItem,
} from '@/components/ui/context-menu';
import { Edit, Trash2, Tag } from 'lucide-react';
import { useBookmarkContext } from '@/context/BookmarkContext';
import { message } from '@/lib/utils/toast';
import { ConfirmDialog, InputDialog } from '@/components/ui/confirm-dialog';
import type { LocalFavoriteItem } from '@/lib/storage/local-storage';

interface ItemContextMenuProps {
  item: LocalFavoriteItem;
  onClose: () => void;
}

export const ItemContextMenu: React.FC<ItemContextMenuProps> = ({
  item,
  onClose
}) => {
  const { updateItem, deleteItem } = useBookmarkContext();

  // 对话框状态管理
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [editTagsDialogOpen, setEditTagsDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const handleRenameItem = () => {
    setRenameDialogOpen(true);
  };

  const handleRenameItemConfirm = async (customTitle: string) => {
    if (!customTitle) {
      message.error('标题不能为空');
      return;
    }

    if (customTitle === (item.custom_title || item.translated_title || item.title)) {
      onClose();
      return;
    }

    try {
      await updateItem(item.local_id, { custom_title: customTitle });
      onClose();
    } catch (error) {
      console.error('重命名收藏项目失败:', error);
      throw error; // 重新抛出错误，让对话框保持打开状态
    }
  };

  const handleEditTags = () => {
    setEditTagsDialogOpen(true);
  };

  const handleEditTagsConfirm = async (userTags: string) => {
    try {
      await updateItem(item.local_id, { user_tags: userTags });
      onClose();
    } catch (error) {
      console.error('编辑标签失败:', error);
      throw error; // 重新抛出错误，让对话框保持打开状态
    }
  };

  const handleDeleteItem = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteItemConfirm = async () => {
    try {
      await deleteItem(item.local_id);
      onClose();
    } catch (error) {
      console.error('删除收藏项目失败:', error);
      throw error; // 重新抛出错误，让对话框保持打开状态
    }
  };


  return (
    <>
      <ContextMenuContent className="w-48">
        <ContextMenuItem onClick={handleRenameItem}>
          <Edit className="mr-2 h-4 w-4" />
          重命名
        </ContextMenuItem>

        <ContextMenuItem onClick={handleEditTags}>
          <Tag className="mr-2 h-4 w-4" />
          编辑标签
        </ContextMenuItem>

        <ContextMenuItem
          onClick={handleDeleteItem}
          className="text-red-600 focus:text-red-600"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          删除
        </ContextMenuItem>
      </ContextMenuContent>

      {/* 重命名项目对话框 */}
      <InputDialog
        open={renameDialogOpen}
        onOpenChange={setRenameDialogOpen}
        title="重命名收藏项目"
        placeholder="请输入新的标题"
        defaultValue={item.custom_title || item.translated_title || item.title}
        onConfirm={handleRenameItemConfirm}
      />

      {/* 编辑标签对话框 */}
      <InputDialog
        open={editTagsDialogOpen}
        onOpenChange={setEditTagsDialogOpen}
        title="编辑标签"
        placeholder="请输入标签，用逗号分隔"
        defaultValue={item.user_tags || ''}
        onConfirm={handleEditTagsConfirm}
      />

      {/* 删除项目确认对话框 */}
      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="删除收藏项目"
        description={`确定要删除"${item.custom_title || item.title}"吗？此操作不可撤销。`}
        confirmText="删除"
        variant="destructive"
        onConfirm={handleDeleteItemConfirm}
      />
    </>
  );
};

export default ItemContextMenu;
