export interface User {
  id: number; // 用户编号，主键
  email: string; // 邮箱
  password: string; // 密码
  username: string; // 用户昵称，不允许为空
  role: 'admin' | 'user' | 'guest'; // 用户角色（admin、user、guest，权限管理）
  avatar_url?: string; // 头像 url
  storage_quota: number; // 存储配额（字节）
  storage_quota_expiry: string; // 存储配额过期时间
  storage_used: number; // 已使用存储（字节）
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
  last_login_at?: string; // 最后登录时间
  phone?: string; // 手机号
  social_type?: string; // 第三方平台类型（如微信、GitHub等）
  social_id?: string; // 第三方登录ID
  preferences?: any; // 用户偏好设置（可用 JSON 存储）
}

// 用于创建用户的类型（不包含自增ID）
export type CreateUserData = Omit<User, 'id'>;

// 用于更新用户的类型（所有字段可选，除了ID）
export type UpdateUserData = Partial<Omit<User, 'id' | 'created_at' | 'updated_at'>> & {
  id: number;
};
