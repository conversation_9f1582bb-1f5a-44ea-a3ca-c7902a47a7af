import { NextRequest, NextResponse } from 'next/server';
import {
  getNotesByUser,
  getNotesByFavoriteItem,
  getFavoriteNoteById,
  createFavoriteNote,
  updateFavoriteNote,
  deleteFavoriteNote,
} from '@/lib/database/cloud_sql';
import type { CreateFavoriteNoteData, UpdateFavoriteNoteData } from '@/types/database/favorite_note';

// GET: /api/database/favorite_note?user_id= 或 ?item_id= 或 ?id=
export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const userId = searchParams.get('user_id');
  const itemId = searchParams.get('item_id');
  const id = searchParams.get('id');

  try {
    let notes = null;

    if (id) {
      // 获取单个笔记
      notes = await getFavoriteNoteById(Number(id));
      if (!notes) {
        return NextResponse.json({ error: '笔记不存在' }, { status: 404 });
      }
    } else if (itemId) {
      // 获取收藏项目的笔记
      notes = await getNotesByFavoriteItem(Number(itemId));
    } else if (userId) {
      // 获取用户的所有笔记
      notes = await getNotesByUser(Number(userId));
    } else {
      return NextResponse.json({ error: '缺少查询参数' }, { status: 400 });
    }

    return NextResponse.json({ success: true, data: notes });
  } catch (error) {
    console.error('获取笔记失败:', error);
    return NextResponse.json({ error: '获取笔记失败' }, { status: 500 });
  }
}

// POST: /api/database/favorite_note
export async function POST(req: NextRequest) {
  try {
    const noteData: CreateFavoriteNoteData = await req.json();

    // 验证必需字段
    if (!noteData.user_id || !noteData.favorite_item_id || !noteData.title) {
      return NextResponse.json({ error: '缺少必需字段' }, { status: 400 });
    }

    const result = await createFavoriteNote(noteData);
    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('创建笔记失败:', error);
    return NextResponse.json({ error: '创建笔记失败' }, { status: 500 });
  }
}

// PUT: /api/database/favorite_note?id=
export async function PUT(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json({ error: 'Missing id parameter' }, { status: 400 });
  }

  try {
    const updates: UpdateFavoriteNoteData = await req.json();
    updates.id = Number(id); // 确保ID正确设置

    const result = await updateFavoriteNote(Number(id), updates);
    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('更新笔记失败:', error);
    return NextResponse.json({ error: '更新笔记失败' }, { status: 500 });
  }
}

// DELETE: /api/database/favorite_note?id=
export async function DELETE(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json({ error: 'Missing id parameter' }, { status: 400 });
  }

  try {
    const result = await deleteFavoriteNote(Number(id));
    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('删除笔记失败:', error);
    return NextResponse.json({ error: '删除笔记失败' }, { status: 500 });
  }
}