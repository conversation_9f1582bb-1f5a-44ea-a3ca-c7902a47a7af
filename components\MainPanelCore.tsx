'use client';

import React from 'react';

import Search from './search/MainPanel';
import { BookmarkDetailView } from './bookmark/sub/BookmarkDetailView';

import { useIconSidebarContext } from '@/context/IconSideBarContext';



export default function MainPanel() {
  const { selectedKey } = useIconSidebarContext();

  const getContentByKey = () => {
    switch (selectedKey) {
      case 'bookmark-detail':
        // 如果是收藏详情模式，显示详情页面
        return <BookmarkDetailView />;

      case 'bookmark':
      case 'search':
      case 'chat':
      default:
        // 其他情况都返回搜索界面
        return <Search />;
    }
  };

  return (
    <div style={{ 
      background: 'white',
      height: '100%',
      width: '100%',
      overflow: 'hidden'
    }}>
      {getContentByKey()}
    </div>
  );
};
