import React, { useState, useRef, useEffect } from "react";
import { Send, StopCircle, Paperclip, Mic } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: (message: string) => void;
  onStop?: () => void;
  disabled?: boolean;
  isStreaming?: boolean;
  placeholder?: string;
  maxLength?: number;
  showAttachment?: boolean;
  showVoice?: boolean;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  value,
  onChange,
  onSend,
  onStop,
  disabled = false,
  isStreaming = false,
  placeholder = "输入消息... (Shift + Enter 换行)",
  maxLength = 2000,
  showAttachment = false,
  showVoice = false,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  // 自动调整高度
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // 先重置高度以获取正确的scrollHeight
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      const minHeight = 40; // 最小高度
      const maxHeight = 120; // 最大高度
      const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));
      textarea.style.height = `${newHeight}px`;
    }
  }, [value]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleSend = () => {
    const trimmedValue = value.trim();
    if (trimmedValue && !disabled) {
      onSend(trimmedValue);
    }
  };

  const handleStop = () => {
    if (onStop) {
      onStop();
    }
  };

  const canSend = value.trim().length > 0 && !disabled;
  const characterCount = value.length;
  const isNearLimit = characterCount > maxLength * 0.8;
  const isOverLimit = characterCount > maxLength;

  return (
    <TooltipProvider>
      <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-white/95 via-white/90 to-transparent dark:from-gray-900/95 dark:via-gray-900/90 backdrop-blur-sm">
        <div className="w-full">
          <div className={cn(
            "relative rounded-xl border transition-all duration-200 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm shadow-lg",
            isFocused
              ? "border-blue-500 shadow-xl ring-2 ring-blue-500/20"
              : "border-gray-200 dark:border-gray-700",
            isOverLimit && "border-red-500 ring-2 ring-red-500/20"
          )}>
            {/* 输入区域 */}
            <div className="flex items-end space-x-2 p-3">
            {/* 附件按钮 */}
            {showAttachment && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100 flex-shrink-0"
                  >
                    <Paperclip className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>添加附件</TooltipContent>
              </Tooltip>
            )}

            {/* 文本输入框 */}
            <Textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => onChange(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder={placeholder}
              disabled={disabled}
              maxLength={maxLength}
              className={cn(
                "min-h-[40px] max-h-[120px] resize-none border-0 bg-transparent p-0 text-sm overflow-hidden",
                "focus-visible:ring-0 focus-visible:ring-offset-0",
                "placeholder:text-gray-500 dark:placeholder:text-gray-400"
              )}
              style={{ height: '40px' }}
            />

            {/* 语音按钮 */}
            {showVoice && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100 flex-shrink-0"
                  >
                    <Mic className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>语音输入</TooltipContent>
              </Tooltip>
            )}

            {/* 发送/停止按钮 */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={isStreaming ? handleStop : handleSend}
                  disabled={!isStreaming && (!canSend || isOverLimit)}
                  size="sm"
                  className={cn(
                    "h-9 w-9 p-0 flex-shrink-0 transition-all duration-200 rounded-lg",
                    isStreaming
                      ? "bg-red-600 hover:bg-red-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105"
                      : canSend && !isOverLimit
                      ? "bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105"
                      : "bg-gray-200 dark:bg-gray-600 text-gray-400 dark:text-gray-500 cursor-not-allowed"
                  )}
                >
                  {isStreaming ? (
                    <StopCircle className="h-4 w-4" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isStreaming ? "停止生成" : "发送消息 (Enter)"}
              </TooltipContent>
            </Tooltip>
          </div>

            {/* 字符计数和提示 */}
            {(characterCount > 0 || isNearLimit) && (
              <div className="flex items-center justify-between px-3 pb-2 text-xs border-t border-gray-100 dark:border-gray-700 pt-2 mt-2">
                <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                  <span>Shift + Enter 换行</span>
                </div>
                <div className={cn(
                  "font-medium transition-colors",
                  isOverLimit
                    ? "text-red-600 dark:text-red-400"
                    : isNearLimit
                    ? "text-yellow-600 dark:text-yellow-400"
                    : "text-gray-500 dark:text-gray-400"
                )}>
                  {characterCount}/{maxLength}
                </div>
              </div>
            )}
          </div>

          {/* 错误提示 */}
          {isOverLimit && (
            <div className="mt-3 text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 px-3 py-2 rounded-lg border border-red-200 dark:border-red-800">
              消息长度超出限制，请缩短后再发送
            </div>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};
