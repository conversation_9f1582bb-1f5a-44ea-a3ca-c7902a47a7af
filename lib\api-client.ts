// API客户端
import { ApiConfig, ModelInfo } from './api-config';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatCompletionRequest {
  model: string;
  messages: ChatMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
}

export interface ChatCompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface ChatCompletionStreamChunk {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    delta: {
      role?: string;
      content?: string;
    };
    finish_reason?: string;
  }[];
}

// API客户端类
export class ApiClient {
  private config: ApiConfig;

  constructor(config: ApiConfig) {
    // 规整配置，避免因空格/多余斜杠导致的鉴权或路由问题
    const baseUrl = (config.baseUrl || '').trim().replace(/\/+$/, '');
    const apiKey = (config.apiKey || '').trim();
    this.config = { ...config, baseUrl, apiKey };
  }

  // 获取模型列表
  async getModels(): Promise<ModelInfo[]> {
    try {
      const response = await fetch(`${this.config.baseUrl}/models`, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`获取模型列表失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // 处理不同提供商的响应格式
      if (data.data && Array.isArray(data.data)) {
        return data.data.map((model: any) => ({
          id: model.id,
          name: model.id,
          provider: this.config.provider,
          description: model.description || '',
        }));
      }

      return [];
    } catch (error) {
      console.error('获取模型列表失败:', error);
      throw error;
    }
  }

  // 测试模型可用性
  async testModel(modelId: string): Promise<boolean> {
    try {
      const response = await this.chatCompletion({
        model: modelId,
        messages: [
          {
            role: 'user',
            content: '请只回答数字：1+1等于几？不要输出任何其他内容或解释。',
          },
        ],
        max_tokens: 10,
        temperature: 0,
      });

      return response.choices?.[0]?.message?.content?.trim() === '2';
    } catch (error) {
      console.error('测试模型失败:', error);
      return false;
    }
  }

  // 聊天完成（非流式）
  async chatCompletion(request: ChatCompletionRequest): Promise<ChatCompletionResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          stream: false,
        }),
      });

      if (!response.ok) {
        const errText = await response.text().catch(() => '');
        console.error('聊天请求失败响应:', {
          status: response.status,
          statusText: response.statusText,
          body: errText?.slice(0, 500),
          baseUrl: this.config.baseUrl,
          model: request.model,
        });
        throw new Error(`聊天请求失败: ${response.status} ${response.statusText}${errText ? ' - ' + errText : ''}`);
      }

      return await response.json();
    } catch (error) {
      console.error('聊天请求失败:', error);
      throw error;
    }
  }

  // 聊天完成（流式）
  async *chatCompletionStream(request: ChatCompletionRequest): AsyncGenerator<ChatCompletionStreamChunk> {
    try {
      const response = await fetch(`${this.config.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream, application/json',
        },
        body: JSON.stringify({
          ...request,
          stream: true,
        }),
      });

      if (!response.ok) {
        const errText = await response.text().catch(() => '');
        console.error('流式聊天请求失败响应:', {
          status: response.status,
          statusText: response.statusText,
          body: errText?.slice(0, 500),
          baseUrl: this.config.baseUrl,
          model: request.model,
        });
        throw new Error(`流式聊天请求失败: ${response.status} ${response.statusText}${errText ? ' - ' + errText : ''}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed === '' || trimmed === 'data: [DONE]') continue;
            
            if (trimmed.startsWith('data: ')) {
              try {
                const jsonStr = trimmed.slice(6);
                const chunk = JSON.parse(jsonStr);
                yield chunk;
              } catch (error) {
                console.warn('解析流式响应失败:', error, trimmed);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      console.error('流式聊天请求失败:', error);
      throw error;
    }
  }
}

// 创建API客户端实例
export function createApiClient(config: ApiConfig): ApiClient {
  return new ApiClient(config);
}
