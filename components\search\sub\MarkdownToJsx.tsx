import React, { useState } from "react";
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import remarkGfm from 'remark-gfm';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';
import { Copy, Check } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { message } from '@/lib/utils/toast';

// 代码块组件
const CodeBlock: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = ''
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    const code = typeof children === 'string' ? children : String(children);
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      message.success('代码已复制');
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      message.error('复制失败');
    }
  };

  // 提取语言信息
  const language = className.replace('lang-', '').replace('language-', '') || 'text';

  return (
    <div className="relative group my-4 w-full min-w-0">
      {/* 语言标签和复制按钮 */}
      <div className="flex items-center justify-between bg-gray-100 dark:bg-gray-800 px-4 py-2 rounded-t-lg border-b border-gray-200 dark:border-gray-700">
        <span className="text-xs font-medium text-gray-600 dark:text-gray-400 uppercase">
          {language}
        </span>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy}
          className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors cursor-pointer"
        >
          {copied ? (
            <Check className="w-3 h-3 text-green-600" />
          ) : (
            <Copy className="w-3 h-3" />
          )}
        </Button>
      </div>
      {/* 代码内容容器 - 支持横向滚动 */}
      <div className="overflow-x-auto overflow-y-visible bg-gray-50 dark:bg-gray-900 rounded-b-lg min-w-0 w-full">
        <pre className="p-4 text-sm font-mono leading-relaxed whitespace-pre" style={{ minWidth: 'max-content' }}>
          <code className="text-gray-800 dark:text-gray-200">
            {children}
          </code>
        </pre>
      </div>
    </div>
  );
};

// 内联代码组件
const InlineCode: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <code className="bg-gray-100 dark:bg-gray-800 rounded px-1 py-0.5 text-sm font-mono text-gray-800 dark:text-gray-200 break-all">
    {children}
  </code>
);

// 表格容器组件 - 支持横向滚动
const TableContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="overflow-x-auto overflow-y-visible my-4 p-2 bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 min-w-0 w-full">
    <div style={{ minWidth: 'max-content' }}>
      {children}
    </div>
  </div>
);

// Markdown渲染组件
export const renderMarkdownToJsx = (content: string) => {
  // 预处理：处理数学公式
  const preprocessed = content
    // 处理独占一行的 [ ... ] 替换为 $$...$$
    .replace(/^[ \t]*\[([\s\S]*?)\][ \t]*$/gm, (_, formula) => `$$${formula.trim()}$$`);

  return (
    // <div className="markdown-content min-w-0 max-w-full overflow-hidden w-full" style={{ maxWidth: '100%', minWidth: '0', contain: 'layout style' }}>
    <div className="markdown-content min-w-0 max-w-full overflow-hidden w-full">
      <ReactMarkdown
        remarkPlugins={[remarkMath, remarkGfm]}
        rehypePlugins={[rehypeKatex]}
        components={{
          // 代码块处理
          pre(props) {
            const { children } = props as any;
            // 获取代码块内容和语言
            const codeElement = React.Children.toArray(children)[0] as any;
            const codeContent = codeElement?.props?.children || '';
            const className = codeElement?.props?.className || '';

            return (
              <CodeBlock className={className}>
                {codeContent}
              </CodeBlock>
            );
          },
          // 内联代码处理
          code(props) {
            const { children, className } = props as any;
            // 如果有 className 说明是代码块内的代码，直接返回
            if (className) {
              return <code className={className}>{children}</code>;
            }
            // 否则是内联代码
            return <InlineCode>{children}</InlineCode>;
          },
          // 表格处理
          table(props) {
            const { children } = props as any;
            return (
              <TableContainer>
                <table className="min-w-full border-collapse border border-gray-300 dark:border-gray-600">
                  {children}
                </table>
              </TableContainer>
            );
          },
          // 表格头
          th(props) {
            const { children } = props as any;
            return (
              <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-100 dark:bg-gray-700 font-semibold text-left">
                {children}
              </th>
            );
          },
          // 表格单元格
          td(props) {
            const { children } = props as any;
            return (
              <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                {children}
              </td>
            );
          },
          // 段落处理
          p(props) {
            const { children } = props as any;
            return (
              <p className="my-2 leading-relaxed text-gray-900 dark:text-gray-100 min-w-0 max-w-full break-words">
                {children}
              </p>
            );
          },
          // 标题处理
          h1(props) {
            const { children } = props as any;
            return <h1 className="text-2xl font-bold mt-6 mb-4 text-gray-900 dark:text-gray-100">{children}</h1>;
          },
          h2(props) {
            const { children } = props as any;
            return <h2 className="text-xl font-semibold mt-5 mb-3 text-gray-900 dark:text-gray-100">{children}</h2>;
          },
          h3(props) {
            const { children } = props as any;
            return <h3 className="text-lg font-semibold mt-4 mb-2 text-gray-900 dark:text-gray-100">{children}</h3>;
          },
          h4(props) {
            const { children } = props as any;
            return <h4 className="text-base font-semibold mt-3 mb-2 text-gray-900 dark:text-gray-100">{children}</h4>;
          },
          h5(props) {
            const { children } = props as any;
            return <h5 className="text-sm font-semibold mt-2 mb-1 text-gray-900 dark:text-gray-100">{children}</h5>;
          },
          h6(props) {
            const { children } = props as any;
            return <h6 className="text-sm font-semibold mt-2 mb-1 text-gray-900 dark:text-gray-100">{children}</h6>;
          },

          // 列表处理
          ul(props) {
            const { children } = props as any;
            return <ul className="list-disc ml-6 my-2 space-y-1">{children}</ul>;
          },
          ol(props) {
            const { children } = props as any;
            return <ol className="list-decimal ml-6 my-2 space-y-1">{children}</ol>;
          },
          li(props) {
            const { children } = props as any;
            return <li className="text-gray-900 dark:text-gray-100">{children}</li>;
          },

          // 引用处理
          blockquote(props) {
            const { children } = props as any;
            return (
              <blockquote className="border-l-4 border-blue-400 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-600 pl-4 pr-2 py-2 my-4 italic text-gray-700 dark:text-gray-300">
                {children}
              </blockquote>
            );
          },

          // 链接处理
          a(props) {
            const { children, href } = props as any;
            return (
              <a
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 underline hover:text-blue-800 dark:hover:text-blue-200 transition-colors break-all cursor-pointer"
              >
                {children}
              </a>
            );
          },

          // 强调处理
          strong(props) {
            const { children } = props as any;
            return <strong className="font-bold text-gray-900 dark:text-gray-100">{children}</strong>;
          },
          em(props) {
            const { children } = props as any;
            return <em className="italic text-gray-700 dark:text-gray-300">{children}</em>;
          },

          // 分割线
          hr() {
            return <hr className="my-6 border-t border-gray-300 dark:border-gray-600" />;
          },
        }}
      >
        {preprocessed}
      </ReactMarkdown>
    </div>
  );
};
