'use client';

import React, { createContext, useContext, useState, useCallback } from 'react';
import { ChevronRight, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
} from '@/components/ui/context-menu';

// 树形节点数据接口
export interface TreeNode {
  id: string;
  label: React.ReactNode;
  icon?: React.ReactNode;
  children?: TreeNode[];
  data?: any;
  disabled?: boolean;
  isFolder?: boolean; // 标识是否为文件夹类型
}

// 树形组件上下文
interface TreeContextValue {
  selectedId?: string;
  expandedIds: Set<string>;
  onSelect?: (id: string, node: TreeNode) => void;
  onExpand?: (id: string, expanded: boolean) => void;
  onDoubleClick?: (id: string, node: TreeNode) => void;
  multiSelect?: boolean;
  selectedIds: Set<string>;
  onMultiSelect?: (ids: string[]) => void;
  // 拖拽相关
  draggable: boolean;
  onDrop?: (dragNodeId: string, dropNodeId: string, dragNode: TreeNode, dropNode: TreeNode) => void;
  canDrop?: (dragNode: TreeNode, dropNode: TreeNode) => boolean;
}

const TreeContext = createContext<TreeContextValue | undefined>(undefined);

const useTreeContext = () => {
  const context = useContext(TreeContext);
  if (!context) {
    throw new Error('Tree components must be used within a Tree');
  }
  return context;
};

// 树形组件属性
interface TreeProps {
  data: TreeNode[];
  selectedId?: string;
  expandedIds?: string[];
  onSelect?: (id: string, node: TreeNode) => void;
  onExpand?: (id: string, expanded: boolean) => void;
  onDoubleClick?: (id: string, node: TreeNode) => void;
  multiSelect?: boolean;
  selectedIds?: string[];
  onMultiSelect?: (ids: string[]) => void;
  className?: string;
  children?: React.ReactNode;
  contextMenuItems?: (node: TreeNode) => Array<{
    label: string;
    onClick: (node: TreeNode) => void;
    icon?: React.ReactNode;
  }>;
  onRootContextMenu?: () => Array<{
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
  }>;
  // 拖拽相关
  draggable?: boolean;
  onDrop?: (dragNodeId: string, dropNodeId: string, dragNode: TreeNode, dropNode: TreeNode) => void;
  canDrop?: (dragNode: TreeNode, dropNode: TreeNode) => boolean;
}

// 主树形组件
export const Tree: React.FC<TreeProps> = ({
  data,
  selectedId,
  expandedIds = [],
  onSelect,
  onExpand,
  onDoubleClick,
  multiSelect = false,
  selectedIds = [],
  onMultiSelect,
  className,
  children,
  contextMenuItems,
  onRootContextMenu,
  draggable = false,
  onDrop,
  canDrop,
}) => {
  const [internalExpandedIds, setInternalExpandedIds] = useState<Set<string>>(
    new Set(expandedIds)
  );
  const [internalSelectedIds, setInternalSelectedIds] = useState<Set<string>>(
    new Set(selectedIds)
  );

  const handleExpand = useCallback((id: string, expanded: boolean) => {
    setInternalExpandedIds(prev => {
      const newSet = new Set(prev);
      if (expanded) {
        newSet.add(id);
      } else {
        newSet.delete(id);
      }
      return newSet;
    });
    onExpand?.(id, expanded);
  }, [onExpand]);

  const handleMultiSelect = useCallback((ids: string[]) => {
    setInternalSelectedIds(new Set(ids));
    onMultiSelect?.(ids);
  }, [onMultiSelect]);

  const contextValue: TreeContextValue = {
    selectedId,
    expandedIds: internalExpandedIds,
    onSelect,
    onExpand: handleExpand,
    onDoubleClick,
    multiSelect,
    selectedIds: internalSelectedIds,
    onMultiSelect: handleMultiSelect,
    draggable,
    onDrop,
    canDrop,
  };

  const rootContextMenuItems = onRootContextMenu ? onRootContextMenu() : [];

  const rootContent = (
    <div className={cn('tree-root h-full w-full', className)}>
      {children || data.map(node => (
        <TreeItem
          key={node.id}
          node={node}
          level={0}
          contextMenuItemsFunction={contextMenuItems}
        />
      ))}
    </div>
  );

  return (
    <TreeContext.Provider value={contextValue}>
      {rootContextMenuItems.length > 0 ? (
        <ContextMenu>
          <ContextMenuTrigger asChild>
            <div className="h-full w-full">
              {rootContent}
            </div>
          </ContextMenuTrigger>
          <ContextMenuContent>
            {rootContextMenuItems.map((item, index) => (
              <ContextMenuItem
                key={index}
                onClick={item.onClick}
                className="flex items-center gap-2"
              >
                {item.icon}
                {item.label}
              </ContextMenuItem>
            ))}
          </ContextMenuContent>
        </ContextMenu>
      ) : (
        rootContent
      )}
    </TreeContext.Provider>
  );
};

// 树形节点组件属性
interface TreeItemProps {
  node: TreeNode;
  level: number;
  contextMenuItemsFunction?: (node: TreeNode) => Array<{
    label: string;
    onClick: (node: TreeNode) => void;
    icon?: React.ReactNode;
  }>;
}

// 树形节点组件
export const TreeItem: React.FC<TreeItemProps> = ({
  node,
  level,
  contextMenuItemsFunction
}) => {
  const {
    selectedId,
    expandedIds,
    onSelect,
    onExpand,
    onDoubleClick,
    multiSelect,
    selectedIds,
    draggable,
    onDrop,
    canDrop,
  } = useTreeContext();

  const hasChildren = node.children && node.children.length > 0;
  const isFolder = node.isFolder || false;
  const shouldShowExpandIcon = isFolder; // 文件夹类型的节点都显示展开符号
  const isExpanded = expandedIds.has(node.id);
  const isSelected = multiSelect
    ? selectedIds.has(node.id)
    : selectedId === node.id;

  const handleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (!node.disabled) {
      onSelect?.(node.id, node);
    }
  }, [node, onSelect]);

  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    if (!node.disabled) {
      // 如果是文件夹，双击时展开/折叠
      if (isFolder) {
        onExpand?.(node.id, !isExpanded);
      }
      onDoubleClick?.(node.id, node);
    }
  }, [node, onDoubleClick, isFolder, isExpanded, onExpand]);

  const handleToggle = useCallback(() => {
    if (isFolder) {
      onExpand?.(node.id, !isExpanded);
    }
  }, [isFolder, node.id, isExpanded, onExpand]);

  // 拖拽处理函数
  const handleDragStart = useCallback((e: React.DragEvent) => {
    if (!draggable) return;
    e.dataTransfer.setData('text/plain', node.id);
    e.dataTransfer.effectAllowed = 'move';
  }, [draggable, node.id]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    if (!draggable) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }, [draggable]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    if (!draggable || !onDrop) return;
    e.preventDefault();
    e.stopPropagation();

    const dragNodeId = e.dataTransfer.getData('text/plain');
    if (dragNodeId === node.id) return; // 不能拖拽到自己

    // 这里需要从外部传入完整的节点数据，暂时使用简化版本
    onDrop(dragNodeId, node.id, { id: dragNodeId } as TreeNode, node);
  }, [draggable, onDrop, node]);

  // 计算当前节点的右键菜单项
  const contextMenuItems = contextMenuItemsFunction ? contextMenuItemsFunction(node) : [];

  const itemContent = (
    <div
      className={cn(
        'flex items-center gap-1 py-1 px-2 rounded-md cursor-pointer hover:bg-accent hover:text-accent-foreground transition-colors',
        isSelected && 'bg-accent text-accent-foreground',
        node.disabled && 'opacity-50 cursor-not-allowed',
        'select-none'
      )}
      style={{ paddingLeft: `${level * 16 + 8}px` }}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      draggable={draggable && !node.disabled}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {shouldShowExpandIcon && (
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0 hover:bg-transparent flex-shrink-0"
          onClick={(e) => {
            e.stopPropagation();
            handleToggle();
          }}
        >
          {isExpanded ? (
            <ChevronDown className="h-3 w-3 flex-shrink-0" />
          ) : (
            <ChevronRight className="h-3 w-3 flex-shrink-0" />
          )}
        </Button>
      )}
      {!shouldShowExpandIcon && <div className="w-4 flex-shrink-0" />}

      <div className="flex-1 truncate text-sm min-w-0">
        {node.label}
      </div>
    </div>
  );

  return (
    <div className="tree-item">
      {contextMenuItems && contextMenuItems.length > 0 ? (
        <ContextMenu>
          <ContextMenuTrigger asChild>
            {itemContent}
          </ContextMenuTrigger>
          <ContextMenuContent>
            {contextMenuItems.map((item, index) => (
              <ContextMenuItem
                key={index}
                onClick={() => item.onClick(node)}
                className="flex items-center gap-2"
              >
                {item.icon}
                {item.label}
              </ContextMenuItem>
            ))}
          </ContextMenuContent>
        </ContextMenu>
      ) : (
        itemContent
      )}
      
      {isFolder && (
        <Collapsible open={isExpanded}>
          <CollapsibleContent className="space-y-0">
            {hasChildren ? (
              node.children?.map(childNode => (
                <TreeItem
                  key={childNode.id}
                  node={childNode}
                  level={level + 1}
                  contextMenuItemsFunction={contextMenuItemsFunction}
                />
              ))
            ) : (
              <div
                className="text-xs text-gray-400 py-1 px-2 ml-8"
                style={{ paddingLeft: `${(level + 1) * 16 + 8}px` }}
              >
                空文件夹
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      )}
    </div>
  );
};

export default Tree;
