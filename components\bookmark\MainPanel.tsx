'use client';

import React from 'react';
import { BookmarkDetailView } from './sub/BookmarkDetailView';
import { useBookmarkContext } from '@/context/BookmarkContext';
import Search from '../search/MainPanel';

export default function BookmarkMainPanel() {
  const { viewMode } = useBookmarkContext();

  // 如果是详情视图，显示详情组件
  if (viewMode === 'detail') {
    return <BookmarkDetailView />;
  }

  // 默认显示搜索界面
  return <Search />;
}
