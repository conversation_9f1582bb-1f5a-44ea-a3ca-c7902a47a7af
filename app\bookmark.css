/* 收藏功能相关样式 */

/* 收藏树形结构样式 */
.bookmark-tree .ant-tree-node-content-wrapper {
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  max-width: 100% !important;
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

.bookmark-tree .ant-tree-node-content-wrapper:hover {
  background-color: #f5f5f5 !important;
}

.bookmark-tree .ant-tree-node-selected .ant-tree-node-content-wrapper {
  background-color: #f5f5f5 !important;
  border: 1px solid #d9d9d9 !important;
}

.bookmark-tree .ant-tree-title {
  font-size: 14px !important;
  line-height: 1.4 !important;
  max-width: 100% !important;
  overflow: hidden !important;
  flex: 1 !important;
  min-width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 确保文件夹和项目标题都能正确截断 */
.bookmark-tree .ant-tree-title > div,
.bookmark-tree .ant-tree-title > span {
  max-width: 100% !important;
  min-width: 0 !important;
  flex: 1 !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/* 文件夹图标和文字横排显示 */
.bookmark-tree .ant-tree-iconEle {
  margin-right: -2px !important;
  flex-shrink: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
}

/* 防止文本拉伸 */
.bookmark-tree .ant-tree-treenode {
  width: 100% !important;
}

/* 确保树节点不会超出容器宽度 */
.bookmark-tree .ant-tree-list-holder-inner {
  width: 100% !important;
}

.bookmark-tree .ant-tree-treenode .ant-tree-node-content-wrapper {
  flex: 1 1 auto !important;
}

/* 文件夹图标样式 */
.bookmark-tree .ant-tree-iconEle {
  margin-right: 3px;
}

/* 收藏项目图标样式 */
.bookmark-tree .ant-tree-node-content-wrapper[data-type="item"] {
  padding-left: 24px;
}

/* 拖拽样式 */
.bookmark-tree .ant-tree-node.drag-over {
  background-color: #f0f9ff;
  border: 2px dashed #1890ff;
  border-radius: 4px;
}

.bookmark-tree .ant-tree-node.drag-over-gap-top {
  border-top: 2px solid #1890ff;
}

.bookmark-tree .ant-tree-node.drag-over-gap-bottom {
  border-bottom: 2px solid #1890ff;
}

/* 右键菜单样式 */
.bookmark-context-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: none;
}

.bookmark-context-menu .ant-menu-item {
  border-radius: 4px;
  margin: 2px 4px;
}

.bookmark-context-menu .ant-menu-item:hover {
  background-color: #f5f5f5;
}

.bookmark-context-menu .ant-menu-item-danger:hover {
  background-color: #fff2f0;
  color: #ff4d4f;
}

/* 收藏按钮动画 */
.bookmark-icon {
  transition: all 0.2s ease;
  cursor: pointer;
}

.bookmark-icon:hover {
  transform: scale(1.1);
}

.bookmark-icon.bookmarked {
  color: #1890ff;
  animation: bookmarkPulse 0.3s ease;
}

.bookmark-icon.bookmarking {
  animation: bookmarkSpin 1s linear infinite;
}

@keyframes bookmarkPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

@keyframes bookmarkSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 详情面板样式 */
.bookmark-detail-panel {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bookmark-detail-panel .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
}

.bookmark-detail-panel .ant-card-body {
  padding: 16px;
}

/* 多行文本截断工具类 */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 标签样式 */
.bookmark-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
  margin: 2px;
  display: inline-block;
}

.bookmark-tag.user-tag {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.bookmark-tag.system-tag {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

/* 笔记编辑器样式 */
.note-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.note-editor .ant-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.note-editor .ant-card-body {
  flex: 1;
  overflow: auto;
}

.note-editor .markdown-preview {
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  min-height: 400px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.note-editor .markdown-preview h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 16px 0 12px 0;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.note-editor .markdown-preview h2 {
  font-size: 20px;
  font-weight: 600;
  margin: 14px 0 10px 0;
  color: #262626;
}

.note-editor .markdown-preview h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 12px 0 8px 0;
  color: #262626;
}

.note-editor .markdown-preview p {
  margin: 8px 0;
  color: #595959;
}

.note-editor .markdown-preview code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #d63384;
}

.note-editor .markdown-preview strong {
  font-weight: 600;
  color: #262626;
}

.note-editor .markdown-preview em {
  font-style: italic;
  color: #595959;
}



/* 开发者工具样式 */
.dev-tools {
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
}

.dev-tools .ant-card-head {
  background-color: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.dev-tools .ant-statistic-title {
  font-size: 12px;
  color: #6c757d;
}

.dev-tools .ant-statistic-content {
  font-size: 16px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bookmark-tree .ant-tree-title {
    font-size: 13px;
  }
  
  .bookmark-detail-panel {
    margin: 8px;
  }
  
  .note-editor .markdown-preview {
    padding: 12px;
    font-size: 14px;
  }
  
  .storage-quota-indicator {
    padding: 6px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .bookmark-tree .ant-tree-node-content-wrapper:hover {
    background-color: #262626;
  }
  
  .bookmark-tree .ant-tree-node-selected .ant-tree-node-content-wrapper {
    background-color: #404040 !important;
    border: 1px solid #595959;
  }
  
  .bookmark-detail-panel {
    background-color: #141414;
    border: 1px solid #303030;
  }
  
  .note-editor .markdown-preview {
    background-color: #1f1f1f;
    border: 1px solid #303030;
    color: #ffffff;
  }
  
  .storage-quota-indicator {
    background-color: #1f1f1f;
    border: 1px solid #303030;
  }
}
