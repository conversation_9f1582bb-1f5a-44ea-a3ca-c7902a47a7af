import { toast as sonnerToast } from 'sonner';

/**
 * 类似 antd message 的 toast 工具函数
 * 提供统一的消息提示接口，替代 antd 的 message 组件
 */
export const message = {
  /**
   * 成功消息
   */
  success: (content: string, duration?: number) => {
    return sonnerToast.success(content, {
      duration: duration || 3000,
      style: {
        background: 'hsl(var(--background))',
        color: 'hsl(var(--foreground))',
        border: '1px solid hsl(142 76% 36%)',
        borderLeft: '4px solid hsl(142 76% 36%)',
      },
    });
  },

  /**
   * 错误消息
   */
  error: (content: string, duration?: number) => {
    return sonnerToast.error(content, {
      duration: duration || 4000,
      style: {
        background: 'hsl(var(--background))',
        color: 'hsl(var(--foreground))',
        border: '1px solid hsl(0 84% 60%)',
        borderLeft: '4px solid hsl(0 84% 60%)',
      },
    });
  },

  /**
   * 警告消息
   */
  warning: (content: string, duration?: number) => {
    return sonnerToast.warning(content, {
      duration: duration || 3500,
      style: {
        background: 'hsl(var(--background))',
        color: 'hsl(var(--foreground))',
        border: '1px solid hsl(38 92% 50%)',
        borderLeft: '4px solid hsl(38 92% 50%)',
      },
    });
  },

  /**
   * 信息消息
   */
  info: (content: string, duration?: number) => {
    return sonnerToast.info(content, {
      duration: duration || 3000,
      style: {
        background: 'hsl(var(--background))',
        color: 'hsl(var(--foreground))',
        border: '1px solid hsl(221 83% 53%)',
        borderLeft: '4px solid hsl(221 83% 53%)',
      },
    });
  },

  /**
   * 加载消息
   */
  loading: (content: string) => {
    return sonnerToast.loading(content, {
      style: {
        background: 'hsl(var(--background))',
        color: 'hsl(var(--foreground))',
        border: '1px solid hsl(var(--border))',
        borderLeft: '4px solid hsl(221 83% 53%)',
      },
    });
  },

  /**
   * 普通消息
   */
  message: (content: string, duration?: number) => {
    return sonnerToast(content, {
      duration: duration || 3000,
      style: {
        background: 'hsl(var(--background))',
        color: 'hsl(var(--foreground))',
        border: '1px solid hsl(var(--border))',
        borderLeft: '4px solid hsl(var(--muted-foreground))',
      },
    });
  },

  /**
   * 销毁所有消息
   */
  destroy: () => {
    sonnerToast.dismiss();
  },
};

// 导出 toast 别名，保持兼容性
export const toast = message;
