"use client"

import { Toaster as Sonner, ToasterProps } from "sonner"

const Toaster = ({ ...props }: ToasterProps) => {
  return (
    <Sonner
      theme="system"
      position="top-center"
      className="toaster group"
      toastOptions={{
        className: "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
        style: {
          padding: '12px 20px',
          fontSize: '14px',
          borderRadius: '8px',
          minWidth: 'auto',
          maxWidth: 'fit-content',
          width: 'auto',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          backdropFilter: 'blur(8px)',
          border: '1px solid hsl(var(--border))',
          background: 'hsl(var(--popover))',
          color: 'hsl(var(--popover-foreground))',
        },
      }}
      style={{
        top: '24px',
        left: '50%',
        transform: 'translateX(-50%)',
        position: 'fixed',
        zIndex: 9999,
        ...props.style,
      }}
      {...props}
    />
  )
}

export { Toaster }
