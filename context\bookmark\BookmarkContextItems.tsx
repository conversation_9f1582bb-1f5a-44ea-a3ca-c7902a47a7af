'use client';

import { useCallback } from 'react';
import { localBookmarkStorage, type LocalFavoriteFolder, type LocalFavoriteItem } from '@/lib/storage/local-storage';
import { message } from '@/lib/utils/toast';
import type { CreateFavoriteItemData, User } from '@/types/database';

/**
 * 书签收藏项目操作相关的 Hook
 * 处理收藏项目的创建、更新、删除、移动等操作
 * 登录用户和未登录用户使用完全隔离的存储空间
 */
export const useBookmarkItems = (
  user: User | null,
  folders: LocalFavoriteFolder[],
  items: LocalFavoriteItem[],
  setItems: React.Dispatch<React.SetStateAction<LocalFavoriteItem[]>>,
  defaultFolderId: string | null,
  updateStorageQuota: () => void
) => {
  // 判断是否为登录用户（使用独立的用户ID存储空间）
  const isLoggedInUser = useCallback(() => {
    return user !== null && user.id > 0;
  }, [user]);

  // 创建收藏项目（根据用户状态选择存储策略）
  const createItem = useCallback(async (
    itemData: Omit<CreateFavoriteItemData, 'user_id' | 'folder_id'>,
    folderLocalId?: string
  ): Promise<LocalFavoriteItem> => {

    // 确定目标文件夹
    let targetFolderId = folderLocalId;

    // 如果没有指定文件夹，根据策略选择文件夹
    if (!targetFolderId) {
      // 检查是否有文件夹
      if (folders.length === 0) {
        message.error('请先创建文件夹再进行收藏');
        throw new Error('没有可用的文件夹');
      }

      // 如果只有一个文件夹，直接使用
      if (folders.length === 1) {
        targetFolderId = folders[0].local_id;
      } else {
        // 多个文件夹时，检查是否设置了默认文件夹
        if (!defaultFolderId) {
          message.error('请先设置一个默认文件夹再进行收藏');
          throw new Error('未设置默认文件夹');
        }
        targetFolderId = defaultFolderId;
      }
    }

    try {
      if (isLoggedInUser()) {
        // 登录用户：使用独立的用户ID存储空间
        const newItem = await localBookmarkStorage.createItem({
          ...itemData,
          user_id: user?.id || 0,
          folder_local_id: targetFolderId,
        });

        setItems(prevItems => [...prevItems, newItem]);
        updateStorageQuota();
        message.success('收藏成功');
        return newItem;
      } else {
        // 未登录用户：使用独立的本地存储空间（ID 0）
        const newItem = await localBookmarkStorage.createItem({
          ...itemData,
          user_id: 0, // 未登录用户统一使用ID 0
          folder_local_id: targetFolderId,
        });

        setItems(prevItems => [...prevItems, newItem]);
        updateStorageQuota();
        message.success('收藏成功');
        return newItem;
      }
    } catch (error) {
      console.error('创建收藏项目失败:', error);
      message.error('收藏失败');
      throw error;
    }
  }, [user?.id, defaultFolderId, folders, updateStorageQuota, setItems, isLoggedInUser]);

  // 更新收藏项目
  const updateItem = useCallback(async (localId: string, updates: Partial<LocalFavoriteItem>) => {
    try {
      await localBookmarkStorage.updateItem(localId, updates);

      // 优化：只更新本地状态中的对应项目
      setItems(prevItems =>
        prevItems.map(item =>
          item.local_id === localId ? { ...item, ...updates } : item
        )
      );
      message.success('更新成功');
    } catch (error) {
      console.error('更新收藏项目失败:', error);
      message.error('更新失败');
      throw error;
    }
  }, [setItems]);

  // 静默更新收藏项目（不显示成功消息，用于自动翻译更新）
  const updateItemSilently = useCallback(async (localId: string, updates: Partial<LocalFavoriteItem>) => {
    try {
      await localBookmarkStorage.updateItem(localId, updates);

      // 优化：只更新本地状态中的对应项目
      setItems(prevItems =>
        prevItems.map(item =>
          item.local_id === localId ? { ...item, ...updates } : item
        )
      );
    } catch (error) {
      console.error('静默更新收藏项目失败:', error);
      throw error;
    }
  }, [setItems]);

  // 更新收藏项目的翻译标题（如果还没有翻译的话）
  const updateItemTranslation = useCallback(async (sourceId: string, sourceType: string, translatedTitle?: string, translatedSummary?: string) => {
    if (!translatedTitle && !translatedSummary) return;

    // 查找对应的收藏项目
    const targetItem = items.find(item =>
      item.source_id === sourceId && item.source_type === sourceType
    );

    if (!targetItem) return;

    // 只有在没有翻译标题时才更新
    const updates: Partial<LocalFavoriteItem> = {};
    if (translatedTitle && !targetItem.translated_title) {
      updates.translated_title = translatedTitle;
    }
    if (translatedSummary && !targetItem.translated_summary) {
      updates.translated_summary = translatedSummary;
    }

    if (Object.keys(updates).length > 0) {
      await updateItemSilently(targetItem.local_id, updates);
    }
  }, [items, updateItemSilently]);

  // 删除收藏项目
  const deleteItem = useCallback(async (localId: string) => {
    try {
      await localBookmarkStorage.deleteItem(localId);

      // 软删除：更新本地状态中的对应项目为已删除状态，然后从显示列表中移除
      setItems(prevItems => prevItems.filter(item => item.local_id !== localId));
      message.success('删除成功');
    } catch (error) {
      console.error('删除收藏项目失败:', error);
      message.error('删除失败');
      throw error;
    }
  }, [setItems]);

  // 移动收藏项目
  const moveItem = useCallback(async (itemLocalId: string, targetFolderLocalId: string) => {
    try {
      await localBookmarkStorage.updateItem(itemLocalId, {
        folder_local_id: targetFolderLocalId,
      });

      // 优化：只更新本地状态中的对应项目
      setItems(prevItems =>
        prevItems.map(item =>
          item.local_id === itemLocalId
            ? { ...item, folder_local_id: targetFolderLocalId }
            : item
        )
      );
      message.success('移动成功');
    } catch (error) {
      console.error('移动收藏项目失败:', error);
      message.error('移动失败');
      throw error;
    }
  }, [setItems]);

  // 检查是否已收藏（支持未登录用户）
  const isItemBookmarked = useCallback(async (sourceId: string, sourceType: string): Promise<boolean> => {
    const userId = user?.id || 0; // 0 表示未登录用户

    try {
      // 先检查本地状态中是否存在且未删除
      const localItem = items.find(item =>
        item.source_id === sourceId &&
        item.source_type === sourceType &&
        item.status !== 'deleted'
      );

      if (localItem) {
        return true;
      }

      // 如果本地状态中没有，再查询存储
      return await localBookmarkStorage.isItemBookmarked(userId, sourceId, sourceType);
    } catch (error) {
      console.error('检查收藏状态失败:', error);
      return false;
    }
  }, [user?.id, items]);

  return {
    createItem,
    updateItem,
    deleteItem,
    moveItem,
    isItemBookmarked,
    updateItemTranslation,
  };
};
