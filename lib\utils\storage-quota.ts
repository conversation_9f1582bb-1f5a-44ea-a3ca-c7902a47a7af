/**
 * 存储配额管理工具
 * 用于检查和管理用户的存储空间使用情况
 */

import type { User } from '@/types/database/user';

// 存储配额相关常量
export const STORAGE_CONSTANTS = {
  // 基础配额（字节）
  FREE_QUOTA: 100 * 1024 * 1024, // 100MB
  PREMIUM_QUOTA: 1024 * 1024 * 1024, // 1GB
  
  // 预留空间（字节）
  RESERVED_SPACE: 10 * 1024 * 1024, // 10MB
  
  // 单个文件大小限制
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  
  // 估算大小
  ESTIMATED_ITEM_SIZE: 5 * 1024, // 5KB per item (metadata)
  ESTIMATED_NOTE_SIZE: 2 * 1024, // 2KB per note
} as const;

// 存储配额状态
export interface StorageQuotaStatus {
  totalQuota: number;
  usedSpace: number;
  availableSpace: number;
  quotaExpiry: Date;
  isExpired: boolean;
  isNearLimit: boolean;
  canAddItems: boolean;
  estimatedItemsLeft: number;
}



/**
 * 检查用户存储配额状态
 */
export function checkStorageQuota(user: User): StorageQuotaStatus {
  const now = new Date();
  const quotaExpiry = new Date(user.storage_quota_expiry);
  const isExpired = now > quotaExpiry;
  
  const totalQuota = user.storage_quota || STORAGE_CONSTANTS.FREE_QUOTA;
  const usedSpace = user.storage_used || 0;
  const availableSpace = Math.max(0, totalQuota - usedSpace);
  
  // 检查是否接近限制（剩余空间少于预留空间）
  const isNearLimit = availableSpace <= STORAGE_CONSTANTS.RESERVED_SPACE;
  
  // 检查是否可以添加新项目
  const canAddItems = !isExpired && !isNearLimit && availableSpace > STORAGE_CONSTANTS.ESTIMATED_ITEM_SIZE;
  
  // 估算还能添加多少项目
  const estimatedItemsLeft = Math.floor(
    (availableSpace - STORAGE_CONSTANTS.RESERVED_SPACE) / STORAGE_CONSTANTS.ESTIMATED_ITEM_SIZE
  );

  return {
    totalQuota,
    usedSpace,
    availableSpace,
    quotaExpiry,
    isExpired,
    isNearLimit,
    canAddItems,
    estimatedItemsLeft: Math.max(0, estimatedItemsLeft),
  };
}



/**
 * 检查是否可以添加新的收藏项目
 */
export function canAddFavoriteItem(user: User, estimatedSize: number = STORAGE_CONSTANTS.ESTIMATED_ITEM_SIZE): {
  canAdd: boolean;
  reason?: string;
} {
  const quotaStatus = checkStorageQuota(user);
  
  if (quotaStatus.isExpired) {
    return {
      canAdd: false,
      reason: '存储配额已过期，请续费后继续使用',
    };
  }
  
  if (quotaStatus.availableSpace < estimatedSize + STORAGE_CONSTANTS.RESERVED_SPACE) {
    return {
      canAdd: false,
      reason: '存储空间不足，请清理收藏或升级存储配额',
    };
  }
  
  return { canAdd: true };
}

/**
 * 检查是否可以上传文件
 */
export function canUploadFile(user: User, fileSize: number): {
  canUpload: boolean;
  reason?: string;
} {
  if (fileSize > STORAGE_CONSTANTS.MAX_FILE_SIZE) {
    return {
      canUpload: false,
      reason: `文件大小超过限制（${formatFileSize(STORAGE_CONSTANTS.MAX_FILE_SIZE)}）`,
    };
  }
  
  const quotaStatus = checkStorageQuota(user);
  
  if (quotaStatus.isExpired) {
    return {
      canUpload: false,
      reason: '存储配额已过期，请续费后继续使用',
    };
  }
  
  if (quotaStatus.availableSpace < fileSize + STORAGE_CONSTANTS.RESERVED_SPACE) {
    return {
      canUpload: false,
      reason: '存储空间不足，请清理文件或升级存储配额',
    };
  }
  
  return { canUpload: true };
}

/**
 * 格式化文件大小显示
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 计算存储使用百分比
 */
export function getStorageUsagePercentage(user: User): number {
  const totalQuota = user.storage_quota || STORAGE_CONSTANTS.FREE_QUOTA;
  const usedSpace = user.storage_used || 0;
  
  return Math.min(100, Math.round((usedSpace / totalQuota) * 100));
}

/**
 * 获取存储状态的颜色（用于UI显示）
 */
export function getStorageStatusColor(percentage: number): string {
  if (percentage >= 90) return '#ff4d4f'; // 红色
  if (percentage >= 75) return '#faad14'; // 橙色
  if (percentage >= 50) return '#1890ff'; // 蓝色
  return '#52c41a'; // 绿色
}

/**
 * 获取存储状态描述
 */
export function getStorageStatusDescription(user: User): string {
  const quotaStatus = checkStorageQuota(user);
  const percentage = getStorageUsagePercentage(user);
  
  if (quotaStatus.isExpired) {
    return '存储配额已过期';
  }
  
  if (percentage >= 95) {
    return '存储空间即将用完';
  }
  
  if (percentage >= 80) {
    return '存储空间使用较多';
  }
  
  if (percentage >= 50) {
    return '存储空间使用正常';
  }
  
  return '存储空间充足';
}

/**
 * 生成存储配额警告信息
 */
export function generateQuotaWarning(user: User): string | null {
  const quotaStatus = checkStorageQuota(user);
  const percentage = getStorageUsagePercentage(user);
  
  if (quotaStatus.isExpired) {
    return '您的存储配额已过期，请续费后继续使用收藏功能。';
  }
  
  if (percentage >= 95) {
    return `存储空间即将用完（${percentage}%），请及时清理收藏或升级存储配额。`;
  }
  
  if (percentage >= 85) {
    return `存储空间使用较多（${percentage}%），建议及时清理不需要的收藏。`;
  }
  
  // 检查配额是否即将过期（7天内）
  const daysUntilExpiry = Math.ceil((quotaStatus.quotaExpiry.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
  if (daysUntilExpiry <= 7 && daysUntilExpiry > 0) {
    return `存储配额将在${daysUntilExpiry}天后过期，请及时续费。`;
  }
  
  return null;
}
