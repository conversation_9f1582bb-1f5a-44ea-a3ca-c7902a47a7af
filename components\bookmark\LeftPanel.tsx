'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { message } from '@/lib/utils/toast';

import { useBookmarkContext } from '@/context/BookmarkContext';
import { useIconSidebarContext } from '@/context/IconSideBarContext';
import { StorageQuotaIndicator } from './sub/StorageQuotaIndicator';
import { BookmarkTree } from './sub/BookmarkTree';

// 移除 Typography，使用 Tailwind CSS 样式

export const Bookmark: React.FC = () => {
  const { selectedItemId, items, setSelectedItem, updateItem } = useBookmarkContext();
  const { setSelectedKey } = useIconSidebarContext();
  const [showDetailPanel, setShowDetailPanel] = useState(false);
  const [isEditingNote, setIsEditingNote] = useState(false);
  const [noteValue, setNoteValue] = useState('');

  const selectedItem = selectedItemId ? items.find(item => item.local_id === selectedItemId) : null;

  // 当选中项目变化时，重置编辑状态
  useEffect(() => {
    if (selectedItem) {
      setNoteValue(selectedItem.user_notes || '');
      setIsEditingNote(false);
    }
  }, [selectedItem]);

  // 开始编辑note
  const handleStartEditNote = () => {
    setIsEditingNote(true);
  };

  // 保存note
  const handleSaveNote = async () => {
    if (!selectedItem) return;

    const trimmedValue = noteValue.trim();
    const originalValue = selectedItem.user_notes || '';

    // 如果内容没有变化，直接退出编辑模式
    if (trimmedValue === originalValue) {
      setIsEditingNote(false);
      return;
    }

    try {
      await updateItem(selectedItem.local_id, {
        user_notes: trimmedValue || undefined
      });
      setIsEditingNote(false);
      message.success('备注保存成功');
    } catch (error) {
      console.error('保存备注失败:', error);
      message.error('保存备注失败');
    }
  };

  // 处理回车键保存
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSaveNote();
    }
  };



  return (
    <div className="h-full w-full flex flex-col">
      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col min-h-0">
        {/* 树形结构 */}
        <div className="flex-1 p-2 overflow-auto">
          <BookmarkTree />
        </div>

        {/* 详情面板 */}
        {selectedItem && (
          <div className="border-t border-gray-100">
            <div className="max-h-48 overflow-auto bg-white border-t border-gray-100">
              <div className="p-3">
                {/* 标题和链接 */}
                {selectedItem.user_tags && (
                  <div>
                    {/* <Text type="secondary" className="text-xs block mb-1">标签：</Text> */}
                    <div className="flex flex-wrap gap-1 mb-2">
                      {selectedItem.user_tags.split(',').map((tag, index) => (
                        <span
                          key={index}
                          className="inline-block bg-blue-100 text-blue-800 text-xs px-1.5 py-0.5 rounded"
                        >
                          {tag.trim()}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* 备注编辑区域 */}
                <div>
                  {/* <Text type="secondary" className="text-xs block mb-1">备注：</Text> */}

                  {isEditingNote ? (
                    <div className="space-y-0">
                      <Textarea
                        value={noteValue}
                        onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setNoteValue(e.target.value)}
                        placeholder="请输入备注..."
                        rows={3}
                        className="text-sm resize-none"
                        onKeyDown={handleKeyDown}
                        onBlur={handleSaveNote}
                        autoFocus
                      />
                    </div>
                  ) : (
                    <div
                      className="text-sm text-gray-700 break-words line-clamp-3 cursor-pointer hover:bg-gray-50 p-0 rounded transition-colors"
                      onClick={handleStartEditNote}
                      title="点击编辑备注"
                    >
                      {selectedItem.user_notes || (
                        <span className="text-gray-400 italic">暂无备注，点击添加</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 存储配额指示器 - 放在最底部 */}
        <div className="border-t border-gray-100 p-2 mt-auto">
          <StorageQuotaIndicator />
        </div>
      </div>
    </div>
  );
};

export default Bookmark;