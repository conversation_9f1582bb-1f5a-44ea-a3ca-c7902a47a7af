# 数据库表结构文档

## User（用户表）

```MySQL
CREATE TABLE IF NOT EXISTS `user` (
  id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '用户编号，主键',
  email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
  password VARCHAR(255) NOT NULL COMMENT '密码',
  username VARCHAR(50) DEFAULT 'Owo' NOT NULL COMMENT '用户昵称，不允许为空',
  role CHAR(10) DEFAULT 'user' COMMENT '用户角色（admin、user、guest，权限管理）',
  avatar_url VARCHAR(255) COMMENT '头像 url',
  storage_quota BIGINT DEFAULT 0 COMMENT '存储配额（字节）',
  storage_quota_expiry DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '存储配额过期时间',
  storage_used BIGINT DEFAULT 0 COMMENT '已使用存储（字节）',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  last_login_at DATETIME COMMENT '最后登录时间',
  phone VARCHAR(20) UNIQUE COMMENT '手机号',
  social_type VARCHAR(20) COMMENT '第三方平台类型（如微信、GitHub等）',
  social_id VARCHAR(100) COMMENT '第三方登录ID',
  preferences JSON COMMENT '用户偏好设置（可用 JSON 存储）',

  INDEX idx_last_login(last_login_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

## LLM 对话

**Conversation（管理对话）**

```
CREATE TABLE IF NOT EXISTS `conversation` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '会话ID，自增主键',
    user_id INT UNSIGNED NOT NULL COMMENT '用户ID，关联User表',
    title VARCHAR(100) NOT NULL COMMENT '会话标题（用户可自定义或自动生成）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '会话创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近更新时间',
    meta JSON COMMENT '会话附加信息',
    INDEX idx_user_id(user_id),
    CONSTRAINT fk_conversation_user FOREIGN KEY (user_id) REFERENCES `user`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话会话表';
```

**ChatMessage（管理对话消息）**

```MySQL
CREATE TABLE IF NOT EXISTS `chat_message` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    conversation_id BIGINT UNSIGNED NOT NULL COMMENT '会话ID，关联Conversation表',
    user_id INT UNSIGNED NOT NULL COMMENT '用户ID，关联User表',
    role ENUM('user', 'assistant', 'system') NOT NULL COMMENT '消息角色',
    content TEXT NOT NULL COMMENT '消息内容',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '消息创建时间',
    meta JSON COMMENT '附加信息',
    INDEX idx_conversation_id(conversation_id),
    CONSTRAINT fk_chat_conversation FOREIGN KEY (conversation_id) REFERENCES `conversation`(id),
    CONSTRAINT fk_chat_user FOREIGN KEY (user_id) REFERENCES `user`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI对话消息表';
```

## 收藏夹系统

**关系图**

```
User
 └─< FavoriteFolder (多级目录, user_id, parent_id)
       └─< FavoriteItem (统一管理论文、PDF、URL等, user_id, folder_id)
               └─< FavoriteNote (每条笔记唯一指向FavoriteItem, 可多条)
Paper (保留用于向量数据库对接)
```

**Paper（管理论文元信息）**

```MySQL
CREATE TABLE IF NOT EXISTS `paper` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '论文ID，自增主键',
    title VARCHAR(255) NOT NULL COMMENT '论文标题',
    authors VARCHAR(500) COMMENT '作者（多个作者用逗号分隔）',
    summary TEXT COMMENT '摘要',
    sub_summary TEXT COMMENT '摘要的进一步精简',
    keywords VARCHAR(255) COMMENT '关键词（逗号分隔）',
    published DATE NOT NULL COMMENT '发表日期',
    source VARCHAR(255) NOT NULL COMMENT '期刊,会议名称',
    doi VARCHAR(100) COMMENT 'DOI标识符',
    pdf_url VARCHAR(512) NOT NULL COMMENT '论文文件存储路径（如OSS路径）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FULLTEXT INDEX idx_fulltext_search (title, summary, keywords),
    INDEX idx_published (published),
    INDEX idx_source (source),
    INDEX idx_authors (authors)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='论文信息表';
```

**PaperReference（管理论文引用信息，暂时不建）**

```MySQL
CREATE TABLE IF NOT EXISTS `paper_reference` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    paper_id BIGINT UNSIGNED NOT NULL COMMENT '本论文ID（引用者）',
    referenced_paper_id BIGINT UNSIGNED NOT NULL COMMENT '被引用论文ID（被引用者）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '建立关系时间',
    CONSTRAINT fk_ref_paper FOREIGN KEY (paper_id) REFERENCES `paper`(id),
    CONSTRAINT fk_ref_referenced FOREIGN KEY (referenced_paper_id) REFERENCES `paper`(id),
    UNIQUE KEY uq_paper_reference (paper_id, referenced_paper_id)  -- 防止重复关系
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='论文引用关系表';
```

**FavoriteFolder（收藏夹文件夹）**

支持多级目录结构，可以放置论文、PDF、URL等各种类型的内容
```MySQL
CREATE TABLE IF NOT EXISTS `favorite_folder` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '收藏夹ID',
    user_id INT UNSIGNED NOT NULL COMMENT '所属用户ID',
    parent_id BIGINT UNSIGNED DEFAULT NULL COMMENT '上级收藏夹ID，顶级为NULL',
    name VARCHAR(100) NOT NULL COMMENT '收藏夹名称',
    icon VARCHAR(50) DEFAULT 'folder' COMMENT '文件夹图标标识（默认folder）',
    color VARCHAR(20) DEFAULT '#1890ff' COMMENT '文件夹颜色（默认蓝色）',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开（用于分享功能）',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_user_id(user_id),
    INDEX idx_parent_id(parent_id),
    INDEX idx_is_public(is_public),

    CONSTRAINT fk_folder_user FOREIGN KEY (user_id) REFERENCES `user`(id),
    CONSTRAINT fk_folder_parent FOREIGN KEY (parent_id) REFERENCES `favorite_folder`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏夹（多级目录）表';
```

// 其中目录层级可以通过以下代码计算
```MySQL
WITH RECURSIVE folder_path AS (
    SELECT id, parent_id, 1 AS level
    FROM FavoriteFolder
    WHERE id = 123  -- 要查询的收藏夹ID

    UNION ALL

    SELECT f.id, f.parent_id, fp.level + 1
    FROM FavoriteFolder f
    JOIN folder_path fp ON f.id = fp.parent_id
)
SELECT MAX(level) AS folder_level
FROM folder_path;
```

**FavoriteItem（收藏的内容项目）**

统一管理论文、PDF、URL、笔记等各种类型的收藏内容，包含完整的论文信息，不依赖Paper表
```MySQL
CREATE TABLE IF NOT EXISTS `favorite_item` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '收藏项目ID',
    user_id INT UNSIGNED NOT NULL COMMENT '所属用户ID',
    folder_id BIGINT UNSIGNED NOT NULL COMMENT '所属收藏夹ID',

    -- 内容类型和来源
    item_type VARCHAR(50) DEFAULT 'note' COMMENT '项目类型（默认note）',
    source_id VARCHAR(255) COMMENT '原始数据源中的ID',
    source_type VARCHAR(50) COMMENT '数据源类型：vector_db, arxiv, pubmed, user_upload, web, google scholar等',

    -- 基础信息（适用于所有类型）
    title VARCHAR(500) NOT NULL COMMENT '标题',
    authors VARCHAR(1000) COMMENT '作者(逗号分隔)',
    summary TEXT COMMENT '摘要',
    sub_summary TEXT COMMENT '摘要的进一步精简',
    keywords VARCHAR(500) COMMENT '关键词(逗号分隔)',
    published DATE COMMENT '发表日期',
    source VARCHAR(255) COMMENT '来源（期刊、会议、网站等）',
    doi VARCHAR(100) COMMENT 'DOI标识符',

    -- 文件相关
    file_url VARCHAR(512) COMMENT '文件存储路径',
    file_size BIGINT COMMENT '文件大小（字节）',
    file_type VARCHAR(50) COMMENT '文件类型（pdf, doc, etc.）',

    -- 用户自定义信息
    custom_title VARCHAR(500) COMMENT '用户自定义标题',
    user_notes TEXT COMMENT '用户备注',
    user_tags VARCHAR(500) COMMENT '用户标签（逗号分隔）',
    translated_title VARCHAR(500) COMMENT '中文标题',
    translated_summary TEXT COMMENT '中文摘要',

    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_user_id(user_id),
    INDEX idx_folder_id(folder_id),
    INDEX idx_item_type(item_type),
    INDEX idx_source_id(source_id),
    INDEX idx_source_type(source_type),
    INDEX idx_published(published),
    FULLTEXT INDEX idx_fulltext_search (title, summary, keywords),

    -- 防止用户在同一文件夹重复收藏相同内容
    UNIQUE KEY uniq_user_source_folder(user_id, source_id, source_type, folder_id),

    CONSTRAINT fk_favitem_user FOREIGN KEY (user_id) REFERENCES `user`(id),
    CONSTRAINT fk_favitem_folder FOREIGN KEY (folder_id) REFERENCES `favorite_folder`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏项目表（统一管理各类内容）';
```

**FavoriteNote（收藏项目的笔记）**
```MySQL
CREATE TABLE IF NOT EXISTS `favorite_note` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '笔记ID',
    user_id INT UNSIGNED NOT NULL COMMENT '所属用户ID',
    favorite_item_id BIGINT UNSIGNED NOT NULL COMMENT '收藏项目ID',
    title VARCHAR(255) NOT NULL COMMENT '笔记标题',
    content TEXT COMMENT '笔记内容',

    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_user_id(user_id),
    INDEX idx_favitem_id(favorite_item_id),

    CONSTRAINT fk_note_user FOREIGN KEY (user_id) REFERENCES `user`(id),
    CONSTRAINT fk_note_favitem FOREIGN KEY (favorite_item_id) REFERENCES `favorite_item`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏项目笔记表';
```

## 新增表

**UserActivity（用户活动日志表）**
```MySQL
CREATE TABLE IF NOT EXISTS `user_activity` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '活动ID',
    user_id INT UNSIGNED NOT NULL COMMENT '用户ID',
    activity_type VARCHAR(50) NOT NULL COMMENT '活动类型：login, logout, upload, download, search, etc.',
    target_type VARCHAR(50) COMMENT '目标类型：folder, item, note, etc.',
    target_id BIGINT UNSIGNED COMMENT '目标ID',
    details JSON COMMENT '活动详情',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_user_id(user_id),
    INDEX idx_activity_type(activity_type),
    INDEX idx_created_at(created_at),

    CONSTRAINT fk_activity_user FOREIGN KEY (user_id) REFERENCES `user`(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户活动日志表';
```

**SystemConfig（系统配置表）**
```MySQL
CREATE TABLE IF NOT EXISTS `system_config` (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    config_type VARCHAR(100) NOT NULL COMMENT '配置类型',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 配置类型和键的组合唯一性
    UNIQUE KEY uniq_config_type_key(config_type, config_key),
    INDEX idx_config_type(config_type),
    INDEX idx_config_key(config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

**UserSearchHistory（用户搜索历史记录表）**
```MySQL
CREATE TABLE IF NOT EXISTS `user_search_history` (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '搜索记录ID',
    user_id INT UNSIGNED NOT NULL COMMENT '用户ID，关联User表',

    -- 搜索内容
    search_query VARCHAR(500) NOT NULL COMMENT '搜索关键词',
    search_type VARCHAR(50) DEFAULT 'general' COMMENT '搜索类型：general, paper, author, keyword等',
    search_source VARCHAR(50) COMMENT '搜索来源：arxiv, pubmed, google_scholar, local等',

    -- 搜索结果统计
    result_count INT DEFAULT 0 COMMENT '搜索结果数量',
    clicked_result_ids JSON COMMENT '用户点击的结果ID列表',

    -- 搜索上下文
    search_filters JSON COMMENT '搜索时使用的过滤条件',
    search_context VARCHAR(100) COMMENT '搜索上下文：bookmark_search, main_search等',

    -- 时间和频率
    search_count INT DEFAULT 1 COMMENT '该查询的搜索次数',
    first_searched_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '首次搜索时间',
    last_searched_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最近搜索时间',

    -- 索引
    INDEX idx_user_id(user_id),
    INDEX idx_search_query(search_query),
    INDEX idx_search_type(search_type),
    INDEX idx_last_searched(last_searched_at),
    INDEX idx_search_count(search_count),

    -- 防重复：同一用户的相同查询合并
    UNIQUE KEY uniq_user_query_type(user_id, search_query, search_type),

    CONSTRAINT fk_search_user FOREIGN KEY (user_id) REFERENCES `user`(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户搜索历史记录表';
```