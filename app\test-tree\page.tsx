'use client';

import React, { useState } from 'react';
import { Tree, TreeNode } from '@/components/ui/tree';
import { Folder, FileText, Star, Edit, Trash2, Plus } from 'lucide-react';

const testData: TreeNode[] = [
  {
    id: 'folder-1',
    label: (
      <div className="flex items-center">
        <Folder className="h-4 w-4 mr-2" />
        <Star className="text-yellow-500 mr-1 h-3 w-3" />
        <span className="truncate">默认收藏夹</span>
      </div>
    ),
    children: [
      {
        id: 'item-1',
        label: (
          <div className="flex items-center">
            <FileText className="h-4 w-4 mr-2" />
            <span className="truncate">测试文档1</span>
          </div>
        ),
      },
      {
        id: 'item-2',
        label: (
          <div className="flex items-center">
            <FileText className="h-4 w-4 mr-2" />
            <span className="truncate">测试文档2</span>
          </div>
        ),
      },
    ],
  },
  {
    id: 'folder-2',
    label: (
      <div className="flex items-center">
        <Folder className="h-4 w-4 mr-2" />
        <span className="truncate">工作文档</span>
      </div>
    ),
    children: [
      {
        id: 'folder-2-1',
        label: (
          <div className="flex items-center">
            <Folder className="h-4 w-4 mr-2" />
            <span className="truncate">项目A</span>
          </div>
        ),
        children: [
          {
            id: 'item-3',
            label: (
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                <span className="truncate">需求文档</span>
              </div>
            ),
          },
          {
            id: 'item-4',
            label: (
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                <span className="truncate">设计文档</span>
              </div>
            ),
          },
        ],
      },
      {
        id: 'item-5',
        label: (
          <div className="flex items-center">
            <FileText className="h-4 w-4 mr-2" />
            <span className="truncate">会议记录</span>
          </div>
        ),
      },
    ],
  },
];

export default function TestTreePage() {
  const [selectedId, setSelectedId] = useState<string>();
  const [expandedIds, setExpandedIds] = useState<string[]>(['folder-1', 'folder-2']);

  const handleSelect = (id: string, node: TreeNode) => {
    setSelectedId(id);
    console.log('Selected:', id, node);
  };

  const handleExpand = (id: string, expanded: boolean) => {
    setExpandedIds(prev => {
      if (expanded) {
        return [...prev, id];
      } else {
        return prev.filter(key => key !== id);
      }
    });
  };

  const handleDoubleClick = (id: string, node: TreeNode) => {
    console.log('Double clicked:', id, node);
  };

  const createContextMenuItems = (node: TreeNode) => {
    const menuItems = [];
    console.log('测试页面 - 创建右键菜单，节点ID:', node.id);

    if (node.id.startsWith('folder-')) {
      menuItems.push(
        {
          label: '重命名',
          onClick: (node: TreeNode) => {
            const newName = prompt('请输入新的文件夹名称:');
            if (newName && newName.trim()) {
              console.log('重命名文件夹:', node, '新名称:', newName);
            }
          },
          icon: <Edit className="h-4 w-4" />
        },
        {
          label: '删除',
          onClick: (node: TreeNode) => {
            if (confirm('确定要删除这个文件夹吗？')) {
              console.log('删除文件夹:', node);
            }
          },
          icon: <Trash2 className="h-4 w-4" />
        },
        {
          label: '新建子文件夹',
          onClick: (node: TreeNode) => {
            const name = prompt('请输入子文件夹名称:');
            if (name && name.trim()) {
              console.log('新建子文件夹:', node, '名称:', name);
            }
          },
          icon: <Plus className="h-4 w-4" />
        }
      );
    } else {
      menuItems.push(
        {
          label: '删除',
          onClick: (node: TreeNode) => {
            if (confirm('确定要删除这个项目吗？')) {
              console.log('删除项目:', node);
            }
          },
          icon: <Trash2 className="h-4 w-4" />
        }
      );
    }

    return menuItems;
  };

  const createRootContextMenuItems = () => {
    return [
      {
        label: '新建文件夹',
        onClick: () => {
          const name = prompt('请输入文件夹名称:');
          if (name && name.trim()) {
            console.log('在根目录创建文件夹:', name);
          }
        },
        icon: <Plus className="h-4 w-4" />
      }
    ];
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Shadcn Tree 组件测试</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">树形组件</h2>
          <div className="h-96 overflow-auto border rounded p-2">
            <Tree
              data={testData}
              selectedId={selectedId}
              expandedIds={expandedIds}
              onSelect={handleSelect}
              onExpand={handleExpand}
              onDoubleClick={handleDoubleClick}
              contextMenuItems={createContextMenuItems}
              onRootContextMenu={createRootContextMenuItems}
              className="w-full"
            />
          </div>
        </div>
        
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-4">状态信息</h2>
          <div className="space-y-2 text-sm">
            <div>
              <strong>选中节点:</strong> {selectedId || '无'}
            </div>
            <div>
              <strong>展开节点:</strong> {expandedIds.join(', ') || '无'}
            </div>
          </div>
          
          <div className="mt-4">
            <h3 className="font-medium mb-2">操作说明:</h3>
            <ul className="text-sm space-y-1 text-gray-600">
              <li>• 点击节点进行选择</li>
              <li>• 双击文件节点查看详情</li>
              <li>• 右键点击显示上下文菜单</li>
              <li>• 点击文件夹图标展开/折叠</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
