import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>ota<PERSON><PERSON>c<PERSON>, Thum<PERSON>Up, ThumbsDown } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Toolt<PERSON>, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { message as toast } from '@/lib/utils/toast';
import { cn } from "@/lib/utils";
import { renderMarkdownToJsx } from '@/components/search/sub/MarkdownToJsx';

interface Message {
  role: "user" | "assistant" | "system";
  content: string;
  timestamp?: Date;
  id?: string;
}

interface MessageBubbleProps {
  message: Message;
  isStreaming?: boolean;
  onCopy?: (content: string) => void;
  onRegenerate?: () => void;
  onFeedback?: (messageId: string, type: 'like' | 'dislike') => void;
}

const TypingIndicator = () => (
  <div className="flex items-center space-x-3 p-3">
    <div className="flex space-x-1">
      <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
      <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
      <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
    </div>
    <span className="text-sm text-gray-600 dark:text-gray-400">AI正在思考...</span>
  </div>
);

const StreamingText: React.FC<{ content: string }> = ({ content }) => {
  return (
    <div className="relative">
      <div className="min-w-0 w-full overflow-hidden">
        {renderMarkdownToJsx(content)}
      </div>
      {/* 流式输入光标 */}
      <span className="inline-block w-2 h-4 bg-blue-500 animate-pulse ml-1 align-text-bottom"></span>
    </div>
  );
};

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isStreaming = false,
  onCopy,
  onRegenerate,
  onFeedback,
}) => {
  const [showActions, setShowActions] = useState(false);
  const [feedback, setFeedback] = useState<'like' | 'dislike' | null>(null);

  // 清理有问题的样式，防止气泡溢出
  useEffect(() => {
    const timer = setTimeout(() => {
      // 查找所有可能导致溢出的元素
      const problematicElements = document.querySelectorAll('[style*="display: table"], [style*="display:table"], [style*="min-width: 100%"], [style*="min-width:100%"]');
      problematicElements.forEach(element => {
        const htmlElement = element as HTMLElement;
        if (htmlElement.style.display === 'table') {
          htmlElement.style.display = 'inline-block';
        }
        if (htmlElement.style.minWidth === '100%') {
          htmlElement.style.minWidth = '0';
        }
        htmlElement.style.maxWidth = '100%';
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [message.content, isStreaming]);

  const handleCopy = () => {
    if (onCopy) {
      onCopy(message.content);
    } else {
      navigator.clipboard.writeText(message.content)
        .then(() => toast.success('复制成功'))
        .catch(() => toast.error('复制失败'));
    }
  };

  const handleFeedback = (type: 'like' | 'dislike') => {
    setFeedback(type);
    if (onFeedback && message.id) {
      onFeedback(message.id, type);
    }
    toast.success(type === 'like' ? '感谢您的反馈！' : '我们会改进的');
  };

  const formatTime = (timestamp?: Date) => {
    if (!timestamp) return '';
    return timestamp.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (message.role === 'user') {
    return (
      <div className="flex justify-end mb-4 group">
        <div className="max-w-[85%] sm:max-w-[75%] md:max-w-[70%]">
          <div className="flex flex-col items-end">
            {message.timestamp && (
              <span className="text-xs text-gray-500 mb-1 opacity-0 group-hover:opacity-100 transition-opacity">
                {formatTime(message.timestamp)}
              </span>
            )}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl rounded-br-md px-4 py-3 shadow-sm">
              <div className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                {message.content}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div 
        className="flex justify-start mb-4 group"
        onMouseEnter={() => setShowActions(true)}
        onMouseLeave={() => setShowActions(false)}
      >
        <div className="flex items-start gap-2 w-full max-w-[85%] min-w-0">

          <div className="flex flex-col flex-1 min-w-0 max-w-[100%] overflow-hidden">
            <div className="flex items-center gap-1 mb-1 min-w-0 overflow-hidden">
              <Badge variant="secondary" className="text-xs flex-shrink-0">
                AI
              </Badge>
              {message.timestamp && (
                <span className="text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity truncate">
                  {formatTime(message.timestamp)}
                </span>
              )}
            </div>
            
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl rounded-tl-md px-4 py-3 shadow-sm min-w-0 w-full max-w-[100%] overflow-hidden" style={{ contain: 'layout style' }}>
            {/* <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-2xl rounded-tl-md px-4 py-3 shadow-sm min-w-0 w-full max-w-[100%] overflow-hidden"> */}
              {isStreaming && !message.content ? (
                <TypingIndicator />
              ) : isStreaming && message.content ? (
                <StreamingText content={message.content} />
              ) : (
                <div className="min-w-0 w-full overflow-hidden" style={{ maxWidth: '100%', minWidth: '0', wordWrap: 'break-word', overflowWrap: 'anywhere' }}>
                {/* <div className="min-w-0 w-full overflow-hidden"> */}
                  {renderMarkdownToJsx(message.content)}
                </div>
              )}
            </div>
            
            {/* 消息操作按钮 */}
            {message.content && (
              <div className={cn(
                "flex items-center space-x-1 mt-3 transition-all duration-200 transform",
                showActions ? "opacity-100 translate-y-0" : "opacity-0 translate-y-1"
              )}>
                <div className="flex items-center space-x-1 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCopy}
                        className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>复制</TooltipContent>
                  </Tooltip>
                
                  {onRegenerate && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={onRegenerate}
                          className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                        >
                          <RotateCcw className="w-3 h-3" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>重新生成</TooltipContent>
                    </Tooltip>
                  )}

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFeedback('like')}
                        className={cn(
                          "h-6 w-6 p-0 rounded-md transition-colors",
                          feedback === 'like'
                            ? "text-green-600 bg-green-100 dark:bg-green-900/30"
                            : "text-gray-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20"
                        )}
                      >
                        <ThumbsUp className="w-3 h-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>有用</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleFeedback('dislike')}
                        className={cn(
                          "h-6 w-6 p-0 rounded-md transition-colors",
                          feedback === 'dislike'
                            ? "text-red-600 bg-red-100 dark:bg-red-900/30"
                            : "text-gray-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
                        )}
                      >
                        <ThumbsDown className="w-3 h-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>无用</TooltipContent>
                  </Tooltip>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};
