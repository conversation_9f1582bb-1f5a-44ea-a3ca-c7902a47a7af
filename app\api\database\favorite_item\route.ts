import { NextRequest, NextResponse } from 'next/server';
import {
  getFavoriteItemsByUser,
  getFavoriteItemsByFolder,
  getFavoriteItemById,
  createFavoriteItem,
  updateFavoriteItem,
  deleteFavoriteItem,
} from '@/lib/database/cloud_sql';
import type { CreateFavoriteItemData, UpdateFavoriteItemData } from '@/types/database/favorite_item';

// GET: /api/database/favorite_item?user_id= 或 ?folder_id= 或 ?id=
export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const userId = searchParams.get('user_id');
  const folderId = searchParams.get('folder_id');
  const id = searchParams.get('id');

  try {
    let items = null;
    
    if (id) {
      // 获取单个收藏项目
      items = await getFavoriteItemById(Number(id));
      if (!items) {
        return NextResponse.json({ error: '收藏项目不存在' }, { status: 404 });
      }
    } else if (folderId) {
      // 获取文件夹中的收藏项目
      items = await getFavoriteItemsByFolder(Number(folderId));
    } else if (userId) {
      // 获取用户的所有收藏项目
      items = await getFavoriteItemsByUser(Number(userId));
    } else {
      return NextResponse.json({ error: '缺少查询参数' }, { status: 400 });
    }

    return NextResponse.json({ success: true, data: items });
  } catch (error) {
    console.error('获取收藏项目失败:', error);
    return NextResponse.json({ error: '获取收藏项目失败' }, { status: 500 });
  }
}

// POST: /api/database/favorite_item
export async function POST(req: NextRequest) {
  try {
    const itemData: CreateFavoriteItemData = await req.json();
    
    // 验证必需字段
    if (!itemData.user_id || !itemData.folder_id || !itemData.title) {
      return NextResponse.json({ error: '缺少必需字段' }, { status: 400 });
    }

    const result = await createFavoriteItem(itemData);
    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('创建收藏项目失败:', error);
    
    // 检查是否是重复收藏错误
    if (error instanceof Error && error.message.includes('Duplicate entry')) {
      return NextResponse.json({ error: '该项目已经收藏过了' }, { status: 409 });
    }
    
    return NextResponse.json({ error: '创建收藏项目失败' }, { status: 500 });
  }
}

// PUT: /api/database/favorite_item?id=
export async function PUT(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');
  
  if (!id) {
    return NextResponse.json({ error: 'Missing id parameter' }, { status: 400 });
  }

  try {
    const updates: UpdateFavoriteItemData = await req.json();
    updates.id = Number(id); // 确保ID正确设置
    
    const result = await updateFavoriteItem(Number(id), updates);
    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('更新收藏项目失败:', error);
    return NextResponse.json({ error: '更新收藏项目失败' }, { status: 500 });
  }
}

// DELETE: /api/database/favorite_item?id=
export async function DELETE(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');
  
  if (!id) {
    return NextResponse.json({ error: 'Missing id parameter' }, { status: 400 });
  }

  try {
    const result = await deleteFavoriteItem(Number(id));
    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('删除收藏项目失败:', error);
    return NextResponse.json({ error: '删除收藏项目失败' }, { status: 500 });
  }
}
