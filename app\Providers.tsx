"use client";

import React from 'react';
import { UserProvider } from '@/context/UserContext';
import { BookmarkProvider } from '@/context/BookmarkContext';
import { IconSidebarProvider } from '@/context/IconSideBarContext';
import { SearchProvider } from '@/context/SearchContext';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <UserProvider>
      <BookmarkProvider>
        <IconSidebarProvider>
          <SearchProvider>
            {children}
          </SearchProvider>
        </IconSidebarProvider>
      </BookmarkProvider>
    </UserProvider>
  );
}

