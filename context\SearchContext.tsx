'use client';

import React, { createContext, useContext, useState } from 'react';
import type { PaperWithExtras as Paper } from '@/types/database/paper';

interface SearchContextProps {
  papers: Paper[];
  setPapers: React.Dispatch<React.SetStateAction<Paper[]>>;
  loading: boolean;
  setLoading: React.Dispatch<React.SetStateAction<boolean>>;
  loadingStr: string;
  setLoadingStr: React.Dispatch<React.SetStateAction<string>>;
  query: string;
  setQuery: React.Dispatch<React.SetStateAction<string>>;
  source: string;
  setSource: React.Dispatch<React.SetStateAction<string>>;
}

const SearchContext = createContext<SearchContextProps | undefined>(undefined);

export const SearchProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [papers, setPapers] = useState<Paper[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingStr, setLoadingStr] = useState('');
  const [query, setQuery] = useState('');
  const [source, setSource] = useState('arxiv');

  return (
    <SearchContext.Provider value={{
      papers, setPapers,
      loading, setLoading,
      loadingStr, setLoadingStr,
      query, setQuery,
      source, setSource
      }}>
      {children}
    </SearchContext.Provider>
  );
};

export const useSearchContext = () => {
  const context = useContext(SearchContext);
  if (!context) throw new Error('useSearchContext must be used within a SearchProvider');
  return context;
};
