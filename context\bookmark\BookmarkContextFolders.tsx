'use client';

import { useCallback } from 'react';
import { localBookmarkStorage, type LocalFavoriteFolder } from '@/lib/storage/local-storage';
import { message } from '@/lib/utils/toast';
import type { CreateFavoriteFolderData, User } from '@/types/database';

/**
 * 书签文件夹操作相关的 Hook
 * 处理文件夹的创建、更新、删除等操作
 * 登录用户和未登录用户使用完全隔离的存储空间
 */
export const useBookmarkFolders = (
  user: User | null,
  folders: LocalFavoriteFolder[],
  setFolders: React.Dispatch<React.SetStateAction<LocalFavoriteFolder[]>>,
  setItems: React.Dispatch<React.SetStateAction<any[]>>,
  defaultFolderId: string | null,
  setDefaultFolderId: (id: string | null) => void
) => {
  // 判断是否为登录用户（使用独立的用户ID存储空间）
  const isLoggedInUser = useCallback(() => {
    return user !== null && user.id > 0;
  }, [user]);

  // 创建文件夹（根据用户状态选择存储策略）
  const createFolder = useCallback(async (folderData: Omit<CreateFavoriteFolderData, 'user_id'> & { parent_local_id?: string }): Promise<LocalFavoriteFolder> => {
    try {
      if (isLoggedInUser()) {
        // 登录用户：使用独立的用户ID存储空间
        const { parent_local_id, ...restData } = folderData;
        const newFolder = await localBookmarkStorage.createFolder({
          ...restData,
          user_id: user?.id || 0,
          parent_local_id,
        });

        setFolders(prevFolders => [...prevFolders, newFolder]);
        message.success('文件夹创建成功');
        return newFolder;
      } else {
        // 未登录用户：使用独立的本地存储空间（ID 0）
        const { parent_local_id, ...restData } = folderData;
        const newFolder = await localBookmarkStorage.createFolder({
          ...restData,
          user_id: 0, // 未登录用户统一使用ID 0
          parent_local_id,
        });

        setFolders(prevFolders => [...prevFolders, newFolder]);
        message.success('文件夹创建成功');
        return newFolder;
      }
    } catch (error) {
      console.error('创建文件夹失败:', error);
      message.error('创建文件夹失败');
      throw error;
    }
  }, [user?.id, setFolders, isLoggedInUser]);

  // 更新文件夹
  const updateFolder = useCallback(async (localId: string, updates: Partial<LocalFavoriteFolder>) => {
    try {
      await localBookmarkStorage.updateFolder(localId, updates);

      // 优化：只更新本地状态中的对应文件夹
      setFolders(prevFolders =>
        prevFolders.map(folder =>
          folder.local_id === localId ? { ...folder, ...updates } : folder
        )
      );
      message.success('文件夹更新成功');
    } catch (error) {
      console.error('更新文件夹失败:', error);
      message.error('更新文件夹失败');
      throw error;
    }
  }, [setFolders]);

  // 递归获取文件夹下的所有子文件夹
  const getSubFolders = useCallback((parentLocalId: string): LocalFavoriteFolder[] => {
    const subFolders: LocalFavoriteFolder[] = [];
    const directChildren = folders.filter(f => f.parent_id === parentLocalId);

    for (const child of directChildren) {
      subFolders.push(child);
      // 递归获取子文件夹的子文件夹
      subFolders.push(...getSubFolders(child.local_id));
    }

    return subFolders;
  }, [folders]);

  // 获取文件夹删除统计信息
  const getFolderDeleteStats = useCallback(async (localId: string) => {
    const targetFolder = folders.find(f => f.local_id === localId);
    if (!targetFolder) {
      throw new Error('文件夹不存在');
    }

    // 获取所有子文件夹（递归）
    const subFolders = getSubFolders(localId);
    const allFolderIds = [localId, ...subFolders.map(f => f.local_id)];

    // 获取所有文件夹中的项目
    let totalItems = 0;
    for (const folderId of allFolderIds) {
      const folderItems = await localBookmarkStorage.getItemsByFolder(folderId);
      totalItems += folderItems.length;
    }

    return {
      folderName: targetFolder.name,
      subFolderCount: subFolders.length,
      totalItemCount: totalItems,
      allFolderIds,
    };
  }, [folders, getSubFolders]);

  // 构建删除确认消息
  const buildDeleteConfirmMessage = useCallback((stats: {
    folderName: string;
    subFolderCount: number;
    totalItemCount: number;
  }) => {
    let message = `确定要删除文件夹"${stats.folderName}"吗？`;
    if (stats.subFolderCount > 0 || stats.totalItemCount > 0) {
      message += '\n\n此操作将同时删除：';
      if (stats.subFolderCount > 0) {
        message += `\n• ${stats.subFolderCount} 个子文件夹`;
      }
      if (stats.totalItemCount > 0) {
        message += `\n• ${stats.totalItemCount} 个收藏项目`;
      }
      message += '\n\n此操作不可撤销！';
    } else {
      message += '\n\n此操作不可撤销。';
    }
    return message;
  }, []);

  // 删除文件夹（返回确认信息，让UI组件处理确认逻辑）
  const deleteFolder = useCallback(async (localId: string, skipConfirmation = false) => {
    // 获取删除统计信息
    const stats = await getFolderDeleteStats(localId);

    // 如果需要确认且有子内容，返回确认信息
    if (!skipConfirmation && (stats.subFolderCount > 0 || stats.totalItemCount > 0)) {
      const confirmMessage = buildDeleteConfirmMessage(stats);
      const error = new Error('NEED_CONFIRMATION') as any;
      error.stats = stats;
      error.confirmMessage = confirmMessage;
      throw error;
    }

    // 执行删除操作
    try {
      // 删除所有文件夹中的项目
      for (const folderId of stats.allFolderIds) {
        const folderItems = await localBookmarkStorage.getItemsByFolder(folderId);
        for (const item of folderItems) {
          await localBookmarkStorage.deleteItem(item.local_id);
        }
      }

      // 删除所有文件夹（从子文件夹开始，避免外键约束问题）
      const foldersToDelete = [...stats.allFolderIds].reverse(); // 反向删除，先删子文件夹
      for (const folderId of foldersToDelete) {
        await localBookmarkStorage.deleteFolder(folderId);
      }

      // 更新本地状态
      setFolders(prevFolders =>
        prevFolders.filter(folder => !stats.allFolderIds.includes(folder.local_id))
      );

      setItems(prevItems =>
        prevItems.filter(item => !stats.allFolderIds.includes(item.folder_local_id))
      );

      // 如果删除的文件夹中包含默认文件夹，清除默认文件夹设置
      if (stats.allFolderIds.includes(defaultFolderId || '')) {
        setDefaultFolderId(null);
      }

      message.success(`文件夹"${stats.folderName}"及其所有内容删除成功`);
    } catch (error) {
      console.error('删除文件夹失败:', error);
      message.error('删除文件夹失败');
      throw error;
    }
  }, [getFolderDeleteStats, buildDeleteConfirmMessage, defaultFolderId, setFolders, setItems, setDefaultFolderId]);

  // 递归删除文件夹及其所有内容
  const deleteFolderRecursively = useCallback(async (localId: string) => {
    try {
      // 获取所有需要删除的文件夹ID
      const stats = await getFolderDeleteStats(localId);

      // 删除所有文件夹中的项目
      for (const folderId of stats.allFolderIds) {
        const folderItems = await localBookmarkStorage.getItemsByFolder(folderId);
        for (const item of folderItems) {
          await localBookmarkStorage.deleteItem(item.local_id);
        }
      }

      // 删除所有文件夹（从子文件夹开始，避免外键约束问题）
      const foldersToDelete = [...stats.allFolderIds].reverse(); // 反向删除，先删子文件夹
      for (const folderId of foldersToDelete) {
        await localBookmarkStorage.deleteFolder(folderId);
      }

      // 更新本地状态
      setFolders(prevFolders =>
        prevFolders.filter(folder => !stats.allFolderIds.includes(folder.local_id))
      );

      setItems(prevItems =>
        prevItems.filter(item => !stats.allFolderIds.includes(item.folder_local_id))
      );

      // 如果删除的文件夹中包含默认文件夹，清除默认文件夹设置
      if (stats.allFolderIds.includes(defaultFolderId || '')) {
        setDefaultFolderId(null);
      }

      message.success(`文件夹"${stats.folderName}"及其所有内容删除成功`);
    } catch (error) {
      console.error('递归删除文件夹失败:', error);
      message.error('删除文件夹失败');
      throw error;
    }
  }, [getFolderDeleteStats, defaultFolderId, setFolders, setItems, setDefaultFolderId]);

  // 设置默认文件夹
  const setDefaultFolder = useCallback(async (localId: string) => {
    setDefaultFolderId(localId);
    message.success('默认文件夹设置成功');
  }, [setDefaultFolderId]);

  return {
    createFolder,
    updateFolder,
    deleteFolder,
    deleteFolderRecursively,
    getFolderDeleteStats,
    buildDeleteConfirmMessage,
    setDefaultFolder,
  };
};
