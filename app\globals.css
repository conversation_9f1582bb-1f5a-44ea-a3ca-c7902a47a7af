@import "tailwindcss";

/* 收藏功能样式 */
@import './bookmark.css';
@import "tw-animate-css";

/* KaTeX 数学公式样式优化 - 解决溢出问题 */
.katex-display {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 transparent;
  max-width: 100% !important;
}

/* 暗色模式下的滚动条 */
.dark .katex-display {
  scrollbar-color: #4a5568 transparent;
}

.dark .katex-display::-webkit-scrollbar-thumb {
  background-color: #4a5568;
}

.dark .katex-display::-webkit-scrollbar-thumb:hover {
  background-color: #2d3748;
}

@custom-variant dark (&:is(.dark *));

/* 简单的过渡动画 */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 面板动画 */
.panel-enter {
  animation: panelSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.panel-exit {
  animation: panelSlideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes panelSlideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes panelSlideOut {
  from {
    opacity: 1;
  }
  to {
    width: 0;
    opacity: 0;
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* 滑条宽度等内容 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background: transparent;
}

/* 滑条本身的样式 */
::-webkit-scrollbar-thumb {
  background: #e7e7e7; /* 你可以换成你想要的颜色 */
  margin: 2px; /* 上 右 下 左，右侧2px间距 */
  border-radius: 3px;
}

/* 滑条的边距等信息 */
::-webkit-scrollbar-track {
  background: transparent;
  margin: 2px; /* 上 右 下 左，右侧2px间距 */
  border-radius: 3px;
}

/* ======================== 自定义 Tree 样式 Start ======================== */

/* 缩小展开/收起按钮的宽度 */
.ant-tree .ant-tree-switcher {
  width: 10px !important;   /* 默认大约 24px，可适当缩小 */
  /* min-width: 8px !important; */
  padding: 0 !important;    /* 去除多余内边距 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 节点内容包裹区（包含标题、图标等）:让高亮区域（蓝色）高度自适应且内容居中 */
.ant-tree .ant-tree-node-content-wrapper {
  flex: 1 1 auto !important;
  display: flex !important;
  align-items: center !important;
  min-height: 16px !important; /* 可根据实际需要调整 */
  padding: 0 4px !important;   /* 左右内边距可调 */
  box-sizing: border-box;
}

.ant-tree .ant-tree-switcher .ant-tree-switcher-icon {
  position: relative;
  top: 1px; /* 或 -1px，微调到完全居中为止 */
  right: -4px;
}

/* 让标题无多余边距 */
.ant-tree .ant-tree-title {
  margin: 0 !important;
  margin-bottom: 2px !important;
  padding: 0 !important;
  line-height: 1.6 !important;
}

/* 缩小 ant-tree 的缩进宽度，让子节点更靠左 */
.ant-tree .ant-tree-indent-unit {
  width: 12px !important;  /* 默认大约 24px，12px 更紧凑 */
}

/* 隐藏 ant-tree 的拖拽图标 */
.ant-tree .ant-tree-draggable-icon {
  display: none !important;
}

/* ======================== 自定义 Select 样式 Start ======================== */
/* 强制覆盖 Antd Select 的主按钮背景色 */
.ant-select-selector {
  background-color: #fff !important; /* 默认白色，可换成你想要的色值 */
  transition: background 0.2s, border-color 0.2s;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.04); /* 轻微阴影效果 */
  border-color: #cdcfd4 !important;
}

.ant-select-selector:hover,
.ant-select-selector:focus {
  background-color: #f3f4f6 !important; /* hover 时的灰色，可换成你想要的色值 */
  border-color: #d1d5db !important; /* hover/聚焦时边框变为灰色（Tailwind 的 gray-300） */
}
/* ======================== 自定义 Select 样式 End ======================== */

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 聊天消息中的代码块样式 */
.prose pre {
  max-width: 100% !important;
  overflow-x: auto !important;
  white-space: pre !important;
  word-wrap: normal !important;
}

.prose code {
  word-break: break-all !important;
  overflow-wrap: anywhere !important;
}

.prose pre code {
  word-break: normal !important;
  overflow-wrap: normal !important;
  white-space: pre !important;
}

/* 确保长文本不会超出容器 */
.message-content {
  word-wrap: break-word;
  overflow-wrap: anywhere;
  max-width: 100%;
}

/* 自定义滚动条样式 */
.markdown-content ::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

.markdown-content ::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.markdown-content ::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.markdown-content ::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 暗色模式滚动条 */
.dark .markdown-content ::-webkit-scrollbar-track {
  background: #374151;
}

.dark .markdown-content ::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .markdown-content ::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 确保聊天气泡内容不会撑大容器 */
.markdown-content {
  width: 100%;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
}

.markdown-content * {
  max-width: 100%;
  min-width: 0;
  box-sizing: border-box;
}

/* 强制所有文本元素遵守宽度限制 */
.markdown-content p,
.markdown-content div,
.markdown-content span,
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  max-width: 100%;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: anywhere;
}

/* 确保KaTeX公式容器不会产生垂直滚动 */
.markdown-content .katex-display {
  overflow-x: auto;
  overflow-y: visible;
  max-width: 100%;
}

.markdown-content .katex {
  max-width: 100%;
}

/* 确保代码块和表格容器的高度自适应 */
.markdown-content pre,
.markdown-content table {
  width: auto;
  min-width: max-content;
  max-width: 100%;
}

/* 强制内联元素也遵守宽度限制 */
.markdown-content code,
.markdown-content kbd,
.markdown-content samp {
  max-width: 100%;
  word-break: break-all;
  overflow-wrap: anywhere;
}

/* 确保列表不会撑大容器 */
.markdown-content ul,
.markdown-content ol,
.markdown-content li {
  max-width: 100%;
  min-width: 0;
}

/* 确保链接和其他内联元素不会撑大容器 */
.markdown-content a,
.markdown-content strong,
.markdown-content em,
.markdown-content b,
.markdown-content i {
  max-width: 100%;
  word-break: break-all;
  overflow-wrap: anywhere;
}

/* 强制所有flex容器和子元素遵守最小宽度为0 */
.group .flex,
.group .flex > *,
.group [class*="flex"] {
  min-width: 0 !important;
}

/* 特别处理聊天气泡中的所有元素 */
.group .bg-white,
.group .dark\\:bg-gray-800 {
  min-width: 0 !important;
  max-width: 100% !important;
  width: 100% !important;
}

/* 确保prose容器严格遵守宽度限制 */
.prose {
  min-width: 0 !important;
  max-width: 100% !important;
  width: 100% !important;
}

.prose * {
  min-width: 0 !important;
  max-width: 100% !important;
}