// API配置管理
export interface ApiProvider {
  id: string;
  name: string;
  baseUrl: string;
  requiresApiKey: boolean;
  supportedFeatures: {
    chat: boolean;
    streaming: boolean;
    reasoning: boolean;
  };
}

export interface ModelInfo {
  id: string;
  name: string;
  provider: string;
  description?: string;
}

export interface ApiConfig {
  provider: string;
  apiKey: string;
  baseUrl: string;
  selectedModel: string;
}

// 支持的API提供商
export const API_PROVIDERS: ApiProvider[] = [
  {
    id: 'modelscope',
    name: '魔搭社区',
    baseUrl: 'https://api-inference.modelscope.cn/v1',
    requiresApiKey: true,
    supportedFeatures: {
      chat: true,
      streaming: true,
      reasoning: true,
    },
  },
  {
    id: 'openai',
    name: 'OpenAI',
    baseUrl: 'https://api.openai.com/v1',
    requiresApiKey: true,
    supportedFeatures: {
      chat: true,
      streaming: true,
      reasoning: false,
    },
  },
  {
    id: 'custom',
    name: '自定义',
    baseUrl: '',
    requiresApiKey: true,
    supportedFeatures: {
      chat: true,
      streaming: true,
      reasoning: true,
    },
  },
];

// 本地存储键名
const STORAGE_KEYS = {
  API_CONFIGS: 'axsight_api_configs',
  SELECTED_MODELS: 'axsight_selected_models',
} as const;

// API配置管理类
export class ApiConfigManager {
  private static instance: ApiConfigManager;
  private configs: Map<string, ApiConfig> = new Map();
  private selectedModels: ModelInfo[] = [];

  private constructor() {
    this.loadFromStorage();
  }

  static getInstance(): ApiConfigManager {
    if (!ApiConfigManager.instance) {
      ApiConfigManager.instance = new ApiConfigManager();
    }
    return ApiConfigManager.instance;
  }

  // 从本地存储加载配置
  private loadFromStorage() {
    if (typeof window === 'undefined') return;

    try {
      const configsData = localStorage.getItem(STORAGE_KEYS.API_CONFIGS);
      if (configsData) {
        const configs = JSON.parse(configsData);
        this.configs = new Map(Object.entries(configs));
      }

      const modelsData = localStorage.getItem(STORAGE_KEYS.SELECTED_MODELS);
      if (modelsData) {
        this.selectedModels = JSON.parse(modelsData);
      }
    } catch (error) {
      console.error('加载API配置失败:', error);
    }
  }

  // 保存到本地存储
  private saveToStorage() {
    if (typeof window === 'undefined') return;

    try {
      const configsObj = Object.fromEntries(this.configs);
      localStorage.setItem(STORAGE_KEYS.API_CONFIGS, JSON.stringify(configsObj));
      localStorage.setItem(STORAGE_KEYS.SELECTED_MODELS, JSON.stringify(this.selectedModels));
    } catch (error) {
      console.error('保存API配置失败:', error);
    }
  }

  // 设置API配置
  setApiConfig(providerId: string, config: ApiConfig) {
    this.configs.set(providerId, config);
    this.saveToStorage();
  }

  // 获取API配置
  getApiConfig(providerId: string): ApiConfig | undefined {
    return this.configs.get(providerId);
  }

  // 获取所有配置
  getAllConfigs(): Map<string, ApiConfig> {
    return new Map(this.configs);
  }

  // 添加选中的模型
  addSelectedModel(model: ModelInfo) {
    const existingIndex = this.selectedModels.findIndex(m => m.id === model.id && m.provider === model.provider);
    if (existingIndex === -1) {
      this.selectedModels.push(model);
      this.saveToStorage();
    }
  }

  // 移除选中的模型
  removeSelectedModel(modelId: string, providerId: string) {
    this.selectedModels = this.selectedModels.filter(m => !(m.id === modelId && m.provider === providerId));
    this.saveToStorage();
  }

  // 获取选中的模型列表
  getSelectedModels(): ModelInfo[] {
    return [...this.selectedModels];
  }

  // 清空配置
  clearConfigs() {
    this.configs.clear();
    this.selectedModels = [];
    this.saveToStorage();
  }
}

// 获取提供商信息
export function getProviderById(id: string): ApiProvider | undefined {
  return API_PROVIDERS.find(p => p.id === id);
}

// 验证API配置
export function validateApiConfig(config: ApiConfig): boolean {
  if (!config.provider || !config.baseUrl) return false;
  
  const provider = getProviderById(config.provider);
  if (!provider) return false;
  
  if (provider.requiresApiKey && !config.apiKey) return false;
  
  return true;
}
