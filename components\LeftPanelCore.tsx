'use client';

import React from 'react';
import { Bookmark } from './bookmark/LeftPanel';
import AuthPanel from './auth/AuthPanel';
import { useIconSidebarContext } from '@/context/IconSideBarContext';


export default function LeftPanel() {
  const { selectedKey } = useIconSidebarContext();

  const getPanelByKey = () => {
    switch (selectedKey) {
      case 'bookmark':
      case 'bookmark-detail':
        return <Bookmark />;

      case 'search':
        return null;

      case 'chat':
        return <Bookmark />; // 聊天暂时使用书签面板

      case 'user':
        return <AuthPanel />;

      // 以后可扩展更多 case
      default:
        return null;
    }
  };

  return (
    <div className="h-full">
      {getPanelByKey()}
    </div>
  );
};