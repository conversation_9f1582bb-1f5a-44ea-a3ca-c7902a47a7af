'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { User, Lock, Mail, Shield } from 'lucide-react';
import { useUserContext } from '@/context/UserContext';
import { message } from '@/lib/utils/toast';

interface RegisterFormData {
  email: string;
  password: string;
  confirmPassword: string;
  username: string;
  verificationCode: string;
}

export default function RegisterPanel() {
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [formData, setFormData] = useState<RegisterFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    verificationCode: ''
  });
  const { login, setIsLoading } = useUserContext();

  // 发送验证码
  const handleSendCode = async () => {
    try {
      if (!formData.email) {
        message.error('请先输入邮箱地址');
        return;
      }

      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        message.error('请输入有效的邮箱地址');
        return;
      }

      setSendingCode(true);

      const response = await fetch('/api/auth/send-verification-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: formData.email }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        message.success('验证码已发送到您的邮箱');
        // 开始倒计时
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      } else {
        message.error(result.error || '发送验证码失败');
      }
    } catch (error) {
      console.error('发送验证码错误:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setSendingCode(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.password !== formData.confirmPassword) {
      message.error('两次输入的密码不一致');
      return;
    }

    setLoading(true);
    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          username: formData.username,
          verificationCode: formData.verificationCode,
        }),
      });

      const result = await response.json();
      console.log('注册API响应:', result);

      if (response.ok && result.success) {
        // 注册成功，自动登录
        login(result.user);
        message.success('注册成功！');
      } else {
        message.error(result.error || '注册失败，请检查输入信息');
      }
    } catch (error) {
      console.error('注册错误:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 h-full bg-white overflow-y-auto">
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-center text-xl">用户注册</CardTitle>
          <p className="text-center text-muted-foreground">创建您的新账户</p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleRegister} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="username" className="text-sm font-medium">用户名</label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  id="username"
                  type="text"
                  placeholder="请输入用户名"
                  value={formData.username}
                  onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                  className="pl-10"
                  required
                  minLength={2}
                  maxLength={20}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">邮箱</label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  id="email"
                  type="email"
                  placeholder="请输入邮箱地址"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="pl-10"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="verificationCode" className="text-sm font-medium">验证码</label>
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                  <Input
                    id="verificationCode"
                    type="text"
                    placeholder="请输入6位验证码"
                    value={formData.verificationCode}
                    onChange={(e) => setFormData({ ...formData, verificationCode: e.target.value })}
                    className="pl-10"
                    required
                    maxLength={6}
                  />
                </div>
                <Button
                  type="button"
                  onClick={handleSendCode}
                  loading={sendingCode}
                  disabled={countdown > 0}
                  variant="outline"
                  className="min-w-[120px]"
                >
                  {countdown > 0 ? `${countdown}s后重发` : '发送验证码'}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium">密码</label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  id="password"
                  type="password"
                  placeholder="请输入密码"
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  className="pl-10"
                  required
                  minLength={6}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="confirmPassword" className="text-sm font-medium">确认密码</label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="请再次输入密码"
                  value={formData.confirmPassword}
                  onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                  className="pl-10"
                  required
                  minLength={6}
                />
              </div>
            </div>

            <Button
              type="submit"
              loading={loading}
              className="w-full"
              size="lg"
            >
              注册
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}