/**
 * 用户搜索历史记录相关类型定义
 */

/**
 * 搜索历史记录数据结构
 * 记录用户的搜索关键词、搜索条件和搜索结果ID
 */

// 搜索来源类型
export type SearchSource = 'arxiv' | 'semantic_scholar' | 'pubmed' | 'all';

// 搜索过滤条件
export interface SearchFilters {
  source?: SearchSource;           // 搜索来源
  year_from?: number;             // 起始年份
  year_to?: number;               // 结束年份
  author?: string;                // 作者筛选
  category?: string;              // 分类筛选（如arXiv分类）
  sort_by?: 'relevance' | 'date' | 'citations'; // 排序方式
  max_results?: number;           // 最大结果数
}

// 搜索历史记录主接口
export interface UserSearchHistory {
  id: number;                     // 历史记录ID
  user_id: number;                // 用户ID（0表示未登录用户）
  search_query: string;           // 搜索关键词
  search_filters: SearchFilters;  // 搜索条件
  result_ids: string[];           // 搜索结果的ID列表（论文ID）
  result_count: number;           // 搜索结果总数
  result_data: string;            // 搜索结果的完整数据（JSON字符串格式）
  created_at: string;             // 搜索时间
  updated_at: string;             // 更新时间
}

// 创建搜索历史记录的数据
export interface CreateSearchHistoryData {
  user_id: number;
  search_query: string;
  search_filters: SearchFilters;
  result_ids: string[];
  result_count: number;
  result_data: string;            // 搜索结果的完整数据（JSON字符串格式）
}

// 搜索历史记录列表项（用于显示）
export interface SearchHistoryItem {
  id: number;
  search_query: string;
  search_filters: SearchFilters;
  result_count: number;
  result_data: string;            // 搜索结果的完整数据（JSON字符串格式）
  created_at: string;
}

export default UserSearchHistory;
