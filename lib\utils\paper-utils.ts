/**
 * 论文数据处理工具函数
 * 用于处理从后端API返回的论文数据格式转换
 */

/**
 * 将作者数组转换为逗号分隔的字符串
 * @param authors 作者数据，可能是字符串或字符串数组
 * @returns 逗号分隔的作者字符串
 */
export function normalizeAuthors(authors: unknown): string | undefined {
  if (!authors) return undefined;
  
  if (Array.isArray(authors)) {
    return authors.filter(Boolean).join(', ');
  }
  
  if (typeof authors === 'string') {
    return authors;
  }
  
  return undefined;
}

/**
 * 将逗号分隔的作者字符串转换为作者数组
 * @param authors 逗号分隔的作者字符串
 * @returns 作者数组
 */
export function parseAuthors(authors?: string | null): string[] {
  if (!authors || typeof authors !== 'string') return [];
  return authors.split(',').map(author => author.trim()).filter(Boolean);
}

/**
 * 将关键词数组转换为逗号分隔的字符串
 * @param keywords 关键词数据，可能是字符串或字符串数组
 * @returns 逗号分隔的关键词字符串
 */
export function normalizeKeywords(keywords: unknown): string | undefined {
  if (!keywords) return undefined;
  
  if (Array.isArray(keywords)) {
    return keywords.filter(Boolean).join(', ');
  }
  
  if (typeof keywords === 'string') {
    return keywords;
  }
  
  return undefined;
}

/**
 * 将逗号分隔的关键词字符串转换为关键词数组
 * @param keywords 逗号分隔的关键词字符串
 * @returns 关键词数组
 */
export function parseKeywords(keywords?: string | null): string[] {
  if (!keywords || typeof keywords !== 'string') return [];
  return keywords.split(',').map(keyword => keyword.trim()).filter(Boolean);
}

/**
 * 将逗号分隔的标签字符串转换为标签数组
 * @param tags 逗号分隔的标签字符串
 * @returns 标签数组
 */
export function parseTags(tags?: string | null): string[] {
  if (!tags || typeof tags !== 'string') return [];
  return tags.split(',').map(tag => tag.trim()).filter(Boolean);
}

/**
 * 规范化从后端API返回的论文数据
 * 确保所有字段都是正确的字符串格式
 * @param rawPaper 从后端API返回的原始论文数据
 * @returns 规范化后的论文数据
 */
export function normalizePaperData(rawPaper: any): any {
  return {
    ...rawPaper,
    authors: normalizeAuthors(rawPaper.authors),
    keywords: normalizeKeywords(rawPaper.keywords),
    // 确保其他字段也是字符串格式
    title: typeof rawPaper.title === 'string' ? rawPaper.title : '',
    summary: typeof rawPaper.summary === 'string' ? rawPaper.summary : undefined,
    published: typeof rawPaper.published === 'string' ? rawPaper.published : undefined,
    source: typeof rawPaper.source === 'string' ? rawPaper.source : undefined,
    doi: typeof rawPaper.doi === 'string' ? rawPaper.doi : undefined,
    pdf_url: typeof rawPaper.pdf_url === 'string' ? rawPaper.pdf_url : undefined,
    translated_title: typeof rawPaper.translated_title === 'string' ? rawPaper.translated_title : undefined,
    translated_summary: typeof rawPaper.translated_summary === 'string' ? rawPaper.translated_summary : undefined,
  };
}
