'use client';

import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { SIDEBAR_ITEMS } from '@/types/ui';
import { TooltipS } from '@/components/ui/tooltip-simple';
import { User } from 'lucide-react';
import { FaSearch } from "react-icons/fa";
import { TbBookmarks } from "react-icons/tb";
import { PiChatTeardropDotsBold } from "react-icons/pi";
import { useUserContext } from '@/context/UserContext';
import type { SidebarKey } from '@/types/ui';

const iconItems: Array<{ key: SidebarKey; icon: React.ReactNode; label: string }> = [
  { key: 'search', icon: <FaSearch size={18}/>, label: '搜索' },
  { key: 'bookmark', icon: <TbBookmarks size={26}/>, label: '收藏' },
  { key: 'chat', icon: <PiChatTeardropDotsBold size={24}/>, label: 'AI 助手' },
];

interface IconSideBarProps {
  selectedKey: SidebarKey;
  onSelect: (key: SidebarKey) => void;
}


const IconSidebar: React.FC<IconSideBarProps> = ({ selectedKey, onSelect }) => {
  const { user } = useUserContext();

  const handleAvatarClick = () => {
    onSelect('user'); // 选择用户相关的key
  };

  return (
    <div
      className="w-12 m-1 flex flex-col items-center"
    >
      {/* 用户头像 */}
      <TooltipS content={user ? user.username : '登录/注册'} side="right">
        <div
          className={`cursor-pointer rounded-full mb-2 mt-2 border-2 transition-colors duration-200
            ${selectedKey === 'user' ? 'border-blue-400 shadow-[0_2px_8px_#e6f4ff]' : 'border-gray-200'}
            hover:border-blue-300`
          }
          onClick={handleAvatarClick}
        >
          <Avatar className="w-10 h-10 bg-white">
            <AvatarImage src={user?.avatar_url || 'https://api.dicebear.com/7.x/pixel-art/svg?seed=cat'} />
            <AvatarFallback>
              <User size={20} />
            </AvatarFallback>
          </Avatar>
        </div>
      </TooltipS>

      {/* 分隔线 */}
      <div className="w-[80%] h-px bg-gray-200 my-2" />

      {/* 图标列表 */}
      {iconItems.map(item => (
        <TooltipS key={item.key} content={item.label} side="right">
          <div
            className={`m-2 cursor-pointer rounded-[8px] transition-colors duration-200
              w-[36px] h-[36px] flex items-center justify-center
              ${selectedKey === item.key ? 'bg-gray-300 shadow-[0_2px_8px_#e6f4ff]' : 'text-gray-500'}
              hover:bg-gray-300`
            }
            onClick={() => onSelect(item.key)}
          >
            {item.icon}
          </div>
        </TooltipS>
      ))}
    </div>
  );
};

export default IconSidebar;