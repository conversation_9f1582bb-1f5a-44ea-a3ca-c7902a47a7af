'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Lock, Mail } from 'lucide-react';
import { useUserContext } from '@/context/UserContext';
import { message } from '@/lib/utils/toast';

interface LoginFormData {
  email: string;
  password: string;
}

interface LoginPanelProps {
  onSwitchToRegister: () => void;
}

export default function LoginPanel({ onSwitchToRegister }: LoginPanelProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<LoginFormData>({ email: '', password: '' });
  const { login, setIsLoading } = useUserContext();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setIsLoading(true);
    
    try {
      // 调用登录API
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // 登录成功，更新用户上下文
        login(result.user);
        message.success('登录成功！');
      } else {
        message.error(result.error || '登录失败，请检查邮箱和密码');
      }
    } catch (error) {
      console.error('登录错误:', error);
      message.error('网络错误，请稍后重试');
    } finally {
      setLoading(false);
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 h-full bg-white">
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-center text-xl">用户登录</CardTitle>
          <p className="text-center text-muted-foreground">欢迎回来，请登录您的账户</p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">邮箱</label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  id="email"
                  type="email"
                  placeholder="请输入邮箱地址"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="pl-10"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium">密码</label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  id="password"
                  type="password"
                  placeholder="请输入密码"
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  className="pl-10"
                  required
                  minLength={6}
                />
              </div>
            </div>

            <Button
              type="submit"
              loading={loading}
              className="w-full"
              size="lg"
            >
              登录
            </Button>
          </form>

          <div className="text-center mt-4">
            <p className="text-muted-foreground">
              还没有账户？
              <Button
                variant="link"
                onClick={onSwitchToRegister}
                className="p-0 ml-1 h-auto"
              >
                立即注册
              </Button>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
