'use client';

import React, { useState } from 'react';
import {
  ContextMenuContent,
  ContextMenuItem,
} from '@/components/ui/context-menu';
import { FolderPlus, Edit, Trash2, Star } from 'lucide-react';
import { useBookmarkContext } from '@/context/BookmarkContext';
import { message } from '@/lib/utils/toast';
import { ConfirmDialog, InputDialog } from '@/components/ui/confirm-dialog';
import type { LocalFavoriteFolder } from '@/lib/storage/local-storage';

interface FolderContextMenuProps {
  folder?: LocalFavoriteFolder;
  isRoot?: boolean;
  onClose: () => void;
}

export const FolderContextMenu: React.FC<FolderContextMenuProps> = ({
  folder,
  isRoot = false,
  onClose
}) => {
  const {
    createFolder,
    updateFolder,
    deleteFolder,
    setDefaultFolder,
    defaultFolderId
  } = useBookmarkContext();

  // 对话框状态管理
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteDialogDescription, setDeleteDialogDescription] = useState('');

  console.log('FolderContextMenu props:', { folder, isRoot });

  const handleCreateFolder = () => {
    setCreateDialogOpen(true);
  };

  const handleCreateFolderConfirm = async (name: string) => {
    if (!name) {
      message.error('文件夹名称不能为空');
      return;
    }

    try {
      await createFolder({
        name,
        parent_id: undefined,
        parent_local_id: folder?.local_id,
        icon: 'folder',
        color: '#1890ff',
        is_public: false,
      });
      onClose();
    } catch (error) {
      console.error('创建文件夹失败:', error);
      throw error; // 重新抛出错误，让对话框保持打开状态
    }
  };

  const handleRenameFolder = () => {
    if (!folder) return;
    setRenameDialogOpen(true);
  };

  const handleRenameFolderConfirm = async (name: string) => {
    if (!folder) return;

    if (!name) {
      message.error('文件夹名称不能为空');
      return;
    }

    if (name === folder.name) {
      onClose();
      return;
    }

    try {
      await updateFolder(folder.local_id, { name });
      onClose();
    } catch (error) {
      console.error('重命名文件夹失败:', error);
      throw error; // 重新抛出错误，让对话框保持打开状态
    }
  };

  const handleDeleteFolder = async () => {
    if (!folder) return;

    try {
      // 尝试删除文件夹，如果需要确认会抛出错误
      await deleteFolder(folder.local_id);
      onClose();
    } catch (error: any) {
      if (error.message === 'NEED_CONFIRMATION') {
        // 设置删除对话框的描述并打开对话框
        setDeleteDialogDescription(error.confirmMessage);
        setDeleteDialogOpen(true);
      } else {
        console.error('删除文件夹失败:', error);
        message.error('删除文件夹失败');
      }
    }
  };

  const handleDeleteFolderConfirm = async () => {
    if (!folder) return;

    try {
      // 跳过确认直接删除
      await deleteFolder(folder.local_id, true);
      onClose();
    } catch (error) {
      console.error('删除文件夹失败:', error);
      throw error; // 重新抛出错误，让对话框保持打开状态
    }
  };

  const handleSetDefaultFolder = async () => {
    if (!folder) return;

    try {
      await setDefaultFolder(folder.local_id);
      onClose();
    } catch (error) {
      console.error('设置默认文件夹失败:', error);
    }
  };

  const isDefaultFolder = folder?.local_id === defaultFolderId;

  return (
    <>
      <ContextMenuContent className="w-48">
        <ContextMenuItem onClick={handleCreateFolder}>
          <FolderPlus className="mr-2 h-4 w-4" />
          新建文件夹
        </ContextMenuItem>

        {folder && !isRoot && (
          <>
            <ContextMenuItem onClick={handleRenameFolder}>
              <Edit className="mr-2 h-4 w-4" />
              重命名
            </ContextMenuItem>

            <ContextMenuItem
              onClick={handleSetDefaultFolder}
              disabled={isDefaultFolder}
            >
              <Star className={`mr-2 h-4 w-4 ${isDefaultFolder ? 'fill-current' : ''}`} />
              {isDefaultFolder ? '默认文件夹' : '设为默认'}
            </ContextMenuItem>

            <ContextMenuItem
              onClick={handleDeleteFolder}
              className="text-red-600 focus:text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              删除
            </ContextMenuItem>
          </>
        )}
      </ContextMenuContent>

      {/* 创建文件夹对话框 */}
      <InputDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        title="创建文件夹"
        placeholder="请输入文件夹名称"
        onConfirm={handleCreateFolderConfirm}
      />

      {/* 重命名文件夹对话框 */}
      <InputDialog
        open={renameDialogOpen}
        onOpenChange={setRenameDialogOpen}
        title="重命名文件夹"
        placeholder="请输入新的文件夹名称"
        defaultValue={folder?.name || ''}
        onConfirm={handleRenameFolderConfirm}
      />

      {/* 删除文件夹确认对话框 */}
      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="删除文件夹"
        description={deleteDialogDescription || `确定要删除文件夹"${folder?.name}"吗？此操作不可撤销。`}
        confirmText="删除"
        variant="destructive"
        onConfirm={handleDeleteFolderConfirm}
      />
    </>
  );
};

export default FolderContextMenu;
