'use client';

import React from 'react';
import { Progress } from '@/components/ui/progress';
import { TooltipS } from '@/components/ui/tooltip-simple';
import { Cloud } from 'lucide-react';
import { useBookmarkContext } from '@/context/BookmarkContext';
import { useUserContext } from '@/context/UserContext';
import {
  formatFileSize,
  getStorageUsagePercentage,
  getStorageStatusColor,
  getStorageStatusDescription
} from '@/lib/utils/storage-quota';

export const StorageQuotaIndicator: React.FC = () => {
  const { user } = useUserContext();
  const { storageQuota, checkQuotaWarning } = useBookmarkContext();

  if (!user || !storageQuota) {
    return null;
  }

  const percentage = getStorageUsagePercentage(user);
  const statusColor = getStorageStatusColor(percentage);
  const statusDescription = getStorageStatusDescription(user);
  const warning = checkQuotaWarning();

  return (
    <TooltipS
      content={
        <div>
          <div>存储使用: {formatFileSize(storageQuota.usedSpace)} / {formatFileSize(storageQuota.totalQuota)}</div>
          <div>状态: {statusDescription}</div>
          {warning && <div className="text-yellow-400">⚠️ {warning}</div>}
        </div>
      }
      side="right"
    >
      <div className="w-full cursor-pointer">
        <div className="flex items-center gap-2 mb-1">
          <Cloud className="flex-shrink-0" size={12} style={{ color: statusColor }} />
          <div className="flex-1 min-w-0">
            <Progress
              value={percentage}
              className="w-full h-2"
            />
          </div>
          <span className="text-xs flex-shrink-0">{percentage}%</span>
        </div>
      </div>
    </TooltipS>
  );
};

export default StorageQuotaIndicator;
