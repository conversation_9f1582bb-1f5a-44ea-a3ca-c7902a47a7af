// 数据库类型统一导出文件

// 枚举定义
export * from './enums';

// 用户相关
export * from './user';

// 对话相关
export * from './conversation';
export * from './chat_message';

// 消息角色类型已在 enums.ts 中导出

// 收藏夹系统
export * from './favorite_folder';
export * from './favorite_item';
export * from './favorite_note';

// 论文（保留用于向量数据库对接）
export * from './paper';

// 系统相关
export * from './user_activity';
export * from './system_config';

// 常用的联合类型
export type DatabaseId = number;
export type DatabaseTimestamp = string;

// API 响应的通用类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 分页相关类型
export interface PaginationParams {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 搜索相关类型
export interface SearchParams {
  query: string;
  filters?: Record<string, any>;
  pagination?: PaginationParams;
}

// 文件上传相关类型
export interface FileUploadInfo {
  filename: string;
  originalName: string;
  size: number;
  mimeType: string;
  url: string;
}

// 用户权限相关类型（UserRole 枚举已在 enums.ts 中定义）

export interface UserPermissions {
  canCreateFolder: boolean;
  canUploadFile: boolean;
  canSharePublic: boolean;
  canAccessAdmin: boolean;
  maxStorageQuota: number;
}
