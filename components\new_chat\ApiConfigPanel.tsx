'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from 'sonner';
import { 
  ArrowLeftIcon, 
  CheckIcon, 
  LoaderIcon, 
  PlusIcon, 
  RefreshCwIcon, 
  TestTubeIcon,
  TrashIcon,
  XIcon 
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { 
  API_PROVIDERS, 
  ApiConfigManager, 
  getProviderById, 
  validateApiConfig,
  type ApiConfig,
  type ModelInfo 
} from '@/lib/api-config';
import { createApiClient } from '@/lib/api-client';

interface ApiConfigPanelProps {
  onBack: () => void;
  onConfigSaved: () => void;
}

export default function ApiConfigPanel({ onBack, onConfigSaved }: ApiConfigPanelProps) {
  const [selectedProvider, setSelectedProvider] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [baseUrl, setBaseUrl] = useState('');
  const [availableModels, setAvailableModels] = useState<ModelInfo[]>([]);
  const [selectedModels, setSelectedModels] = useState<ModelInfo[]>([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [testingModels, setTestingModels] = useState<Set<string>>(new Set());

  const configManager = ApiConfigManager.getInstance();

  useEffect(() => {
    // 加载已选择的模型
    setSelectedModels(configManager.getSelectedModels());
  }, []);

  useEffect(() => {
    // 当选择提供商时，自动填充基础URL和加载已保存的配置
    if (selectedProvider) {
      const provider = getProviderById(selectedProvider);
      if (provider) {
        // 统一去掉尾部斜杠
        const url = (provider.baseUrl || '').replace(/\/+$/, '');
        setBaseUrl(url);

        // 加载已保存的配置
        const savedConfig = configManager.getApiConfig(selectedProvider);
        if (savedConfig) {
          setApiKey(savedConfig.apiKey?.trim() || '');
          if (selectedProvider === 'custom') {
            setBaseUrl((savedConfig.baseUrl || '').replace(/\/+$/, ''));
          }
        }
      }
    }
  }, [selectedProvider]);

  // 获取模型列表
  const handleGetModels = async () => {
    if (!selectedProvider || !apiKey || !baseUrl) {
      toast.error('请先填写完整的API配置');
      return;
    }

    setIsLoadingModels(true);
    try {
      const config: ApiConfig = {
        provider: selectedProvider,
        apiKey,
        baseUrl,
        selectedModel: '',
      };

      // 自动保存配置，避免用户忘记点击“保存配置”
      configManager.setApiConfig(selectedProvider, config);
      onConfigSaved();

      const client = createApiClient(config);
      const models = await client.getModels();
      setAvailableModels(models);
      toast.success(`成功获取 ${models.length} 个模型`);
    } catch (error) {
      console.error('获取模型列表失败:', error);
      toast.error('获取模型列表失败，请检查API配置');
    } finally {
      setIsLoadingModels(false);
    }
  };

  // 测试模型
  const handleTestModel = async (model: ModelInfo) => {
    if (!selectedProvider || !apiKey || !baseUrl) {
      toast.error('请先填写完整的API配置');
      return;
    }

    const modelKey = `${model.provider}-${model.id}`;
    setTestingModels(prev => new Set(prev).add(modelKey));

    try {
      const config: ApiConfig = {
        provider: selectedProvider,
        apiKey,
        baseUrl,
        selectedModel: model.id,
      };

      const client = createApiClient(config);
      const isWorking = await client.testModel(model.id);
      
      if (isWorking) {
        toast.success(`模型 ${model.name} 测试通过`);
      } else {
        toast.error(`模型 ${model.name} 测试失败`);
      }
    } catch (error) {
      console.error('测试模型失败:', error);
      toast.error(`模型 ${model.name} 测试失败`);
    } finally {
      setTestingModels(prev => {
        const newSet = new Set(prev);
        newSet.delete(modelKey);
        return newSet;
      });
    }
  };

  // 添加模型到选中列表
  const handleAddModel = (model: ModelInfo) => {
    configManager.addSelectedModel(model);
    setSelectedModels(configManager.getSelectedModels());
    toast.success(`已添加模型 ${model.name}`);
    // 通知父组件模型列表已更新
    onConfigSaved();
  };

  // 从选中列表移除模型
  const handleRemoveModel = (model: ModelInfo) => {
    configManager.removeSelectedModel(model.id, model.provider);
    setSelectedModels(configManager.getSelectedModels());
    toast.success(`已移除模型 ${model.name}`);
    // 通知父组件模型列表已更新
    onConfigSaved();
  };

  // 保存配置
  const handleSaveConfig = () => {
    if (!selectedProvider || !apiKey || !baseUrl) {
      toast.error('请填写完整的API配置');
      return;
    }

    const config: ApiConfig = {
      provider: selectedProvider,
      apiKey,
      baseUrl,
      selectedModel: '',
    };

    if (!validateApiConfig(config)) {
      toast.error('API配置验证失败');
      return;
    }

    configManager.setApiConfig(selectedProvider, config);
    toast.success('API配置已保存');
    onConfigSaved();
  };

  const isModelSelected = (model: ModelInfo) => {
    return selectedModels.some(m => m.id === model.id && m.provider === model.provider);
  };

  const isModelTesting = (model: ModelInfo) => {
    const modelKey = `${model.provider}-${model.id}`;
    return testingModels.has(modelKey);
  };

  return (
    <div className="flex h-full w-full flex-col overflow-hidden rounded-xl border bg-background shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between border-b bg-muted/50 px-4 py-3">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="h-8 px-2"
          >
            <ArrowLeftIcon className="size-4" />
            <span className="ml-1">返回</span>
          </Button>
          <div className="h-4 w-px bg-border" />
          <span className="font-medium text-sm">API配置</span>
        </div>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-6">
          {/* 提供商选择 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">选择API提供商</CardTitle>
              <CardDescription>选择您要使用的AI服务提供商</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="provider">提供商</Label>
                <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择提供商" />
                  </SelectTrigger>
                  <SelectContent>
                    {API_PROVIDERS.map((provider) => (
                      <SelectItem key={provider.id} value={provider.id}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedProvider && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="apiKey">API密钥</Label>
                    <Input
                      id="apiKey"
                      type="password"
                      value={apiKey}
                      onChange={(e) => setApiKey(e.target.value)}
                      placeholder="输入您的API密钥"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="baseUrl">基础URL</Label>
                    <Input
                      id="baseUrl"
                      value={baseUrl}
                      onChange={(e) => setBaseUrl(e.target.value)}
                      placeholder="API基础URL"
                      disabled={selectedProvider !== 'custom'}
                    />
                  </div>

                  <div className="flex gap-2">
                    <Button onClick={handleGetModels} disabled={isLoadingModels}>
                      {isLoadingModels ? (
                        <LoaderIcon className="size-4 animate-spin" />
                      ) : (
                        <RefreshCwIcon className="size-4" />
                      )}
                      <span className="ml-1">获取模型列表</span>
                    </Button>
                    <Button onClick={handleSaveConfig} variant="outline">
                      保存配置
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* 可用模型列表 */}
          {availableModels.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">可用模型</CardTitle>
                <CardDescription>选择您要使用的模型</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {availableModels.map((model) => (
                    <div
                      key={`${model.provider}-${model.id}`}
                      className="flex items-center justify-between p-2 border rounded-lg"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">{model.name}</div>
                        {model.description && (
                          <div className="text-xs text-muted-foreground truncate">
                            {model.description}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-2 ml-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTestModel(model)}
                          disabled={isModelTesting(model)}
                        >
                          {isModelTesting(model) ? (
                            <LoaderIcon className="size-3 animate-spin" />
                          ) : (
                            <TestTubeIcon className="size-3" />
                          )}
                        </Button>
                        {isModelSelected(model) ? (
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => handleRemoveModel(model)}
                          >
                            <XIcon className="size-3" />
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            onClick={() => handleAddModel(model)}
                          >
                            <PlusIcon className="size-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 已选择的模型 */}
          {selectedModels.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">已选择的模型</CardTitle>
                <CardDescription>这些模型将出现在聊天界面的模型选择列表中</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {selectedModels.map((model) => (
                    <div
                      key={`${model.provider}-${model.id}`}
                      className="flex items-center justify-between p-2 border rounded-lg"
                    >
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{getProviderById(model.provider)?.name}</Badge>
                        <span className="font-medium text-sm">{model.name}</span>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleRemoveModel(model)}
                      >
                        <TrashIcon className="size-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
