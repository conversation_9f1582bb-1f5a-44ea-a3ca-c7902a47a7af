// 数据库相关的枚举定义

// 消息角色枚举
export enum MessageRole {
  USER = 'user',
  ASSISTANT = 'assistant',
  SYSTEM = 'system'
}

// 收藏项目类型枚举
export enum ItemType {
  PAPER = 'paper',
  PDF = 'pdf',
  URL = 'url',
  NOTE = 'note'
}

// 数据源类型枚举
export enum SourceType {
  VECTOR_DB = 'vector_db',
  ARXIV = 'arxiv',
  PUBMED = 'pubmed',
  USER_UPLOAD = 'user_upload',
  WEB = 'web',
  GOOGLE_SCHOLAR = 'google_scholar'
}

// 用户角色枚举
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest'
}

// 文件夹图标枚举
export enum FolderIcon {
  FOLDER = 'folder',
  STAR = 'star',
  HEART = 'heart',
  BOOKMARK = 'bookmark',
  TAG = 'tag',
  ARCHIVE = 'archive'
}


