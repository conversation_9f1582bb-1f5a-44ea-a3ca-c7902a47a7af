'use client';

import React, { useState, useCallback } from 'react';
import {
  Folder,
  FolderOpen,
  FileText,
  Star,
  Plus,
  Edit,
  Trash2
} from 'lucide-react';
import { Tree, TreeNode } from '@/components/ui/tree';
import { Button } from '@/components/ui/button';
import { ConfirmDialog, InputDialog } from '@/components/ui/confirm-dialog';
import { useBookmarkContext } from '@/context/BookmarkContext';
import { useIconSidebarContext } from '@/context/IconSideBarContext';
import { message } from '@/lib/utils/toast';

import type { LocalFavoriteFolder, LocalFavoriteItem } from '@/lib/storage/local-storage';

interface BookmarkTreeNode extends TreeNode {
  type: 'folder' | 'item';
  data: LocalFavoriteFolder | LocalFavoriteItem;
}

export const BookmarkTree: React.FC = () => {
  const {
    folders,
    items,
    loading,
    defaultFolderId,
    setSelectedFolder,
    setSelectedItem,
    setViewMode,
    createFolder,
    updateFolder,
    deleteFolder,
    setDefaultFolder,
    updateItem,
    deleteItem,
    moveItem,
  } = useBookmarkContext();

  const { setSelectedKey } = useIconSidebarContext();

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedTreeKey, setSelectedTreeKey] = useState<string | undefined>();

  // 对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    title: string;
    description: string;
    onConfirm: () => void | Promise<void>;
    variant?: 'default' | 'destructive';
  }>({
    open: false,
    title: '',
    description: '',
    onConfirm: () => {},
  });

  const [inputDialog, setInputDialog] = useState<{
    open: boolean;
    title: string;
    description?: string;
    placeholder?: string;
    defaultValue?: string;
    onConfirm: (value: string) => void | Promise<void>;
  }>({
    open: false,
    title: '',
    onConfirm: () => {},
  });

  // 构建树形数据结构
  const buildTreeData = useCallback((): BookmarkTreeNode[] => {
    const folderMap = new Map<string, BookmarkTreeNode>();
    const rootNodes: BookmarkTreeNode[] = [];

    // 创建文件夹节点
    folders.forEach(folder => {
      const isExpanded = expandedKeys.includes(`folder-${folder.local_id}`);
      const node: BookmarkTreeNode = {
        id: `folder-${folder.local_id}`,
        label: (
          <div className="flex items-center min-w-0">
            {isExpanded ? <FolderOpen className="h-4 w-4 mr-2 flex-shrink-0" /> : <Folder className="h-4 w-4 mr-2 flex-shrink-0" />}
            <span className="truncate min-w-0">{folder.name}</span>
            {folder.local_id === defaultFolderId && (
              <Star className="text-yellow-500 ml-1 h-4 w-4 flex-shrink-0" />
            )}
          </div>
        ),
        type: 'folder',
        data: folder,
        children: [],
        isFolder: true, // 标识为文件夹类型
      };
      folderMap.set(folder.local_id, node);
    });

    // 构建文件夹层级关系
    folders.forEach(folder => {
      const node = folderMap.get(folder.local_id);
      if (!node) return;

      if (folder.parent_id) {
        const parentNode = folderMap.get(String(folder.parent_id));
        if (parentNode && parentNode.children) {
          parentNode.children.push(node);
        }
      } else {
        rootNodes.push(node);
      }
    });

    // 添加收藏项目到对应文件夹
    items.forEach(item => {
      const folderNode = folderMap.get(item.folder_local_id);
      if (folderNode && folderNode.children) {
        const itemNode: BookmarkTreeNode = {
          id: `item-${item.local_id}`,
          label: (
            <div className="flex items-center min-w-0">
              <div className="w-1 flex-shrink-0" /> {/* 占位符，与文件夹的展开符号对齐 */}
              <FileText className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="truncate min-w-0">{item.custom_title || item.translated_title || item.title}</span>
            </div>
          ),
          type: 'item',
          data: item,
        };
        folderNode.children.push(itemNode);
      }
    });

    return rootNodes;
  }, [folders, items, defaultFolderId, expandedKeys]);

  const treeData = buildTreeData();

  // 处理节点选择
  const handleSelect = (id: string, node: TreeNode) => {
    const [type, nodeId] = id.split('-');
    setSelectedTreeKey(id);

    if (type === 'folder') {
      setSelectedFolder(nodeId);
      setSelectedItem(null);
    } else if (type === 'item') {
      setSelectedItem(nodeId);
      setSelectedFolder(null);
    }
  };

  // 处理双击事件
  const handleDoubleClick = (id: string, node: TreeNode) => {
    const bookmarkNode = node as BookmarkTreeNode;
    if (bookmarkNode.type === 'item') {
      setSelectedItem(bookmarkNode.data.local_id);
      setViewMode('detail'); // 切换到详情视图模式
      setSelectedKey('bookmark-detail'); // 设置 selectedKey 为 bookmark-detail
    }
  };

  // 处理展开/折叠
  const handleExpand = (id: string, expanded: boolean) => {
    setExpandedKeys(prev => {
      if (expanded) {
        return [...prev, id];
      } else {
        return prev.filter(key => key !== id);
      }
    });
  };

  // 创建右键菜单项
  const createContextMenuItems = (node: TreeNode) => {
    const bookmarkNode = node as BookmarkTreeNode;
    const menuItems = [];

    if (bookmarkNode.type === 'folder') {
      menuItems.push(
        {
          label: '重命名',
          onClick: (node: TreeNode) => {
            const bookmarkNode = node as BookmarkTreeNode;
            const folder = bookmarkNode.data as LocalFavoriteFolder;
            setInputDialog({
              open: true,
              title: '重命名文件夹',
              placeholder: '请输入新的文件夹名称',
              defaultValue: folder.name,
              onConfirm: async (newName: string) => {
                if (newName !== folder.name) {
                  try {
                    await updateFolder(folder.local_id, { name: newName });
                  } catch (error) {
                    console.error('重命名文件夹失败:', error);
                    message.error('重命名文件夹失败');
                  }
                }
              },
            });
          },
          icon: <Edit className="h-4 w-4" />
        },
        {
          label: bookmarkNode.data.local_id === defaultFolderId ? '默认文件夹' : '设为默认',
          onClick: (node: TreeNode) => {
            const bookmarkNode = node as BookmarkTreeNode;
            const folder = bookmarkNode.data as LocalFavoriteFolder;
            if (folder.local_id !== defaultFolderId) {
              setDefaultFolder(folder.local_id);
            }
          },
          icon: <Star className={`h-4 w-4 ${bookmarkNode.data.local_id === defaultFolderId ? 'text-yellow-500' : ''}`} />
        },
        {
          label: '删除',
          onClick: async (node: TreeNode) => {
            const bookmarkNode = node as BookmarkTreeNode;
            const folder = bookmarkNode.data as LocalFavoriteFolder;

            try {
              // 尝试删除文件夹，如果需要确认会抛出错误
              await deleteFolder(folder.local_id);
            } catch (error: any) {
              if (error.message === 'NEED_CONFIRMATION') {
                // 显示确认对话框
                setConfirmDialog({
                  open: true,
                  title: '删除文件夹',
                  description: error.confirmMessage,
                  variant: 'destructive',
                  onConfirm: async () => {
                    try {
                      // 跳过确认直接删除
                      await deleteFolder(folder.local_id, true);
                    } catch (deleteError) {
                      console.error('删除文件夹失败:', deleteError);
                      message.error('删除文件夹失败');
                    }
                  },
                });
              } else {
                console.error('删除文件夹失败:', error);
                message.error('删除文件夹失败');
              }
            }
          },
          icon: <Trash2 className="h-4 w-4" />
        },
        {
          label: '新建子文件夹',
          onClick: (node: TreeNode) => {
            const bookmarkNode = node as BookmarkTreeNode;
            const folder = bookmarkNode.data as LocalFavoriteFolder;
            setInputDialog({
              open: true,
              title: '新建子文件夹',
              placeholder: '请输入子文件夹名称',
              onConfirm: async (name: string) => {
                try {
                  await createFolder({
                    name: name,
                    parent_id: undefined, // 数据库字段，本地不使用
                    parent_local_id: folder.local_id, // 本地父文件夹ID
                    icon: 'folder',
                    color: '#1890ff',
                    is_public: false,
                  });
                } catch (error) {
                  console.error('创建子文件夹失败:', error);
                  message.error('创建子文件夹失败');
                }
              },
            });
          },
          icon: <Plus className="h-4 w-4" />
        }
      );
    } else if (bookmarkNode.type === 'item') {
      menuItems.push(
        {
          label: '重命名',
          onClick: (node: TreeNode) => {
            const bookmarkNode = node as BookmarkTreeNode;
            const item = bookmarkNode.data as LocalFavoriteItem;
            setInputDialog({
              open: true,
              title: '重命名收藏项目',
              placeholder: '请输入新的标题',
              defaultValue: item.custom_title || item.translated_title || item.title,
              onConfirm: async (customTitle: string) => {
                if (customTitle !== (item.custom_title || item.translated_title || item.title)) {
                  try {
                    await updateItem(item.local_id, { custom_title: customTitle });
                  } catch (error) {
                    console.error('重命名收藏项目失败:', error);
                    message.error('重命名收藏项目失败');
                  }
                }
              },
            });
          },
          icon: <Edit className="h-4 w-4" />
        },
        {
          label: '删除',
          onClick: (node: TreeNode) => {
            const bookmarkNode = node as BookmarkTreeNode;
            const item = bookmarkNode.data as LocalFavoriteItem;
            const itemTitle = item.custom_title || item.translated_title || item.title || '未命名项目';

            setConfirmDialog({
              open: true,
              title: '删除收藏项目',
              description: `确定要删除收藏项目"${itemTitle}"吗？此操作不可撤销。`,
              variant: 'destructive',
              onConfirm: async () => {
                try {
                  await deleteItem(item.local_id);
                } catch (error) {
                  console.error('删除收藏项目失败:', error);
                  message.error('删除收藏项目失败');
                }
              },
            });
          },
          icon: <Trash2 className="h-4 w-4" />
        }
      );
    }

    return menuItems;
  };

  // 创建根目录右键菜单项
  const createRootContextMenuItems = () => {
    return [
      {
        label: '新建文件夹',
        onClick: () => {
          setInputDialog({
            open: true,
            title: '新建文件夹',
            placeholder: '请输入文件夹名称',
            onConfirm: async (name: string) => {
              try {
                await createFolder({
                  name: name,
                  parent_id: undefined,
                  icon: 'folder',
                  color: '#1890ff',
                  is_public: false,
                });
              } catch (error) {
                console.error('创建文件夹失败:', error);
                message.error('创建文件夹失败');
              }
            },
          });
        },
        icon: <Plus className="h-4 w-4" />
      }
    ];
  };

  // 处理拖拽功能
  const handleDrop = useCallback(async (dragNodeId: string, dropNodeId: string, dragNode: TreeNode, dropNode: TreeNode) => {
    try {
      // 解析节点ID获取类型和本地ID
      const [dragType, dragLocalId] = dragNodeId.split('-');
      const [dropType, dropLocalId] = dropNodeId.split('-');

      // 只允许拖拽到文件夹
      if (dropType !== 'folder') {
        message.error('只能拖拽到文件夹中');
        return;
      }

      if (dragType === 'folder') {
        // 拖拽文件夹：更新父文件夹（使用本地ID）
        await updateFolder(dragLocalId, { parent_id: dropLocalId });
        message.success('文件夹移动成功');
      } else if (dragType === 'item') {
        // 拖拽项目：移动到目标文件夹
        await moveItem(dragLocalId, dropLocalId);
      }
    } catch (error) {
      console.error('拖拽操作失败:', error);
      message.error('拖拽操作失败');
    }
  }, [updateFolder, moveItem]);

  // 检查是否可以拖拽
  const canDrop = useCallback((dragNode: TreeNode, dropNode: TreeNode) => {
    const [dragType] = dragNode.id.split('-');
    const [dropType] = dropNode.id.split('-');

    // 只允许拖拽到文件夹
    return dropType === 'folder';
  }, []);

  // 创建文件夹
  const handleCreateFolder = async () => {
    try {
      await createFolder({
        name: '新建文件夹',
        parent_id: undefined,
        icon: 'folder',
        color: '#1890ff',
        is_public: false,
      });
    } catch (error) {
      console.error('创建文件夹失败:', error);
      message.error('创建文件夹失败:' + error);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="h-full">
      {treeData.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-500 mb-4">暂无收藏夹</div>
          <Button
            onClick={handleCreateFolder}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            创建收藏夹
          </Button>
        </div>
      ) : (
        <Tree
          data={treeData}
          selectedId={selectedTreeKey}
          expandedIds={expandedKeys}
          onSelect={handleSelect}
          onExpand={handleExpand}
          onDoubleClick={handleDoubleClick}
          contextMenuItems={createContextMenuItems}
          onRootContextMenu={createRootContextMenuItems}
          draggable={true}
          onDrop={handleDrop}
          canDrop={canDrop}
          className="bookmark-tree"
        />
      )}

      {/* 确认对话框 */}
      <ConfirmDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}
        title={confirmDialog.title}
        description={confirmDialog.description}
        variant={confirmDialog.variant}
        onConfirm={confirmDialog.onConfirm}
      />

      {/* 输入对话框 */}
      <InputDialog
        open={inputDialog.open}
        onOpenChange={(open) => setInputDialog(prev => ({ ...prev, open }))}
        title={inputDialog.title}
        description={inputDialog.description}
        placeholder={inputDialog.placeholder}
        defaultValue={inputDialog.defaultValue}
        onConfirm={inputDialog.onConfirm}
      />
    </div>
  );
};

export default BookmarkTree;
