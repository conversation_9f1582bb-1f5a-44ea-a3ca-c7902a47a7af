export interface Conversation {
  id: number; // 会话ID
  user_id: number; // 用户ID
  title: string; // 会话标题
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
}

// 用于创建会话的类型（不包含自增ID和时间戳）
export type CreateConversationData = Omit<Conversation, 'id' | 'created_at' | 'updated_at'>;

// 用于更新会话的类型（所有字段可选，除了ID）
export type UpdateConversationData = Partial<Omit<Conversation, 'id' | 'created_at' | 'updated_at'>> & {
  id: number;
};
