'use client';

import React, { createContext, useContext, useState } from 'react';
import { useUserContext } from './UserContext';
import type { LocalFavoriteFolder, LocalFavoriteItem } from '@/lib/storage/local-storage';
import type { CreateFavoriteFolderData, CreateFavoriteItemData } from '@/types/database';

// 导入切分的功能模块
import { useBookmarkStorage } from './bookmark/BookmarkContextStorage';
import { useBookmarkFolders } from './bookmark/BookmarkContextFolders';
import { useBookmarkItems } from './bookmark/BookmarkContextItems';

// 导入类型定义
import type { StorageQuotaStatus } from '@/lib/utils/storage-quota';

interface BookmarkContextProps {
  // 数据状态
  folders: LocalFavoriteFolder[];
  items: LocalFavoriteItem[];
  loading: boolean;
  
  // 选中状态
  selectedFolderId: string | null;
  selectedItemId: string | null;
  defaultFolderId: string | null;

  // 视图状态（note 模式已移除）
  viewMode: 'list' | 'detail';
  setViewMode: (mode: 'list' | 'detail') => void;

  // 存储配额状态
  storageQuota: StorageQuotaStatus | null;
  checkQuotaWarning: () => string | null;

  // 文件夹操作
  createFolder: (folderData: Omit<CreateFavoriteFolderData, 'user_id'> & { parent_local_id?: string }) => Promise<LocalFavoriteFolder>;
  updateFolder: (localId: string, updates: Partial<LocalFavoriteFolder>) => Promise<void>;
  deleteFolder: (localId: string, skipConfirmation?: boolean) => Promise<void>;
  deleteFolderRecursively: (localId: string) => Promise<void>;
  getFolderDeleteStats: (localId: string) => Promise<{
    folderName: string;
    subFolderCount: number;
    totalItemCount: number;
    allFolderIds: string[];
  }>;
  buildDeleteConfirmMessage: (stats: {
    folderName: string;
    subFolderCount: number;
    totalItemCount: number;
  }) => string;
  setDefaultFolder: (localId: string) => Promise<void>;

  // 收藏项目操作
  createItem: (itemData: Omit<CreateFavoriteItemData, 'user_id' | 'folder_id'>, folderLocalId?: string) => Promise<LocalFavoriteItem>;
  updateItem: (localId: string, updates: Partial<LocalFavoriteItem>) => Promise<void>;
  deleteItem: (localId: string) => Promise<void>;
  moveItem: (itemLocalId: string, targetFolderLocalId: string) => Promise<void>;
  isItemBookmarked: (sourceId: string, sourceType: string) => Promise<boolean>;
  updateItemTranslation: (sourceId: string, sourceType: string, translatedTitle?: string, translatedSummary?: string) => Promise<void>;

  // 选择操作
  setSelectedFolder: (localId: string | null) => void;
  setSelectedItem: (localId: string | null) => void;

  // 数据刷新
  refreshData: () => Promise<void>;
}

const BookmarkContext = createContext<BookmarkContextProps | undefined>(undefined);

export const BookmarkProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useUserContext();

  // 基础状态
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [defaultFolderId, setDefaultFolderId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'detail'>('list');

  // 使用切分的功能模块
  const {
    folders,
    items,
    loading,
    storageQuota,
    checkQuotaWarning,
    updateStorageQuota,
    refreshData,
    setFolders,
    setItems,
  } = useBookmarkStorage(user, defaultFolderId);

  const {
    createFolder,
    updateFolder,
    deleteFolder,
    deleteFolderRecursively,
    getFolderDeleteStats,
    buildDeleteConfirmMessage,
    setDefaultFolder,
  } = useBookmarkFolders(user, folders, setFolders, setItems, defaultFolderId, setDefaultFolderId);

  const {
    createItem,
    updateItem,
    deleteItem,
    moveItem,
    isItemBookmarked,
    updateItemTranslation,
  } = useBookmarkItems(user, folders, items, setItems, defaultFolderId, updateStorageQuota);

  const value: BookmarkContextProps = {
    // 数据状态
    folders,
    items,
    loading,

    // 选中状态
    selectedFolderId,
    selectedItemId,
    defaultFolderId,

    // 视图状态
    viewMode,
    setViewMode,

    // 存储配额状态
    storageQuota,
    checkQuotaWarning,

    // 文件夹操作
    createFolder,
    updateFolder,
    deleteFolder,
    deleteFolderRecursively,
    getFolderDeleteStats,
    buildDeleteConfirmMessage,
    setDefaultFolder,
    
    // 收藏项目操作
    createItem,
    updateItem,
    deleteItem,
    moveItem,
    isItemBookmarked,
    updateItemTranslation,
    
    // 选择操作
    setSelectedFolder: setSelectedFolderId,
    setSelectedItem: setSelectedItemId,
    
    // 数据刷新
    refreshData: () => refreshData(true), // 默认保持默认文件夹设置
  };

  return (
    <BookmarkContext.Provider value={value}>
      {children}
    </BookmarkContext.Provider>
  );
};

export const useBookmarkContext = () => {
  const context = useContext(BookmarkContext);
  if (!context) {
    throw new Error('useBookmarkContext 必须在 BookmarkProvider 内使用');
  }
  return context;
};

export default BookmarkContext;
