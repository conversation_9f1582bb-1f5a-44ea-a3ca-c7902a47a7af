import { NextRequest, NextResponse } from 'next/server';
import {
  getFoldersByUser,
  createFavoriteFolder,
  updateFavoriteFolder,
  deleteFavoriteFolder,
} from '@/lib/database/cloud_sql';
import type { CreateFavoriteFolderData } from '@/types/database/favorite_folder';

// GET: /api/database/favorite_folder?user_id=
export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const userId = searchParams.get('user_id');

  if (!userId) {
    return NextResponse.json({ error: 'Missing user_id parameter' }, { status: 400 });
  }

  try {
    const folders = await getFoldersByUser(Number(userId));
    return NextResponse.json({ success: true, data: folders });
  } catch (error) {
    console.error('获取收藏夹失败:', error);
    return NextResponse.json({ error: '获取收藏夹失败' }, { status: 500 });
  }
}

// POST: /api/database/favorite_folder
export async function POST(req: NextRequest) {
  try {
    const folderData: CreateFavoriteFolderData = await req.json();

    // 验证必需字段
    if (!folderData.user_id || !folderData.name) {
      return NextResponse.json({ error: '缺少必需字段' }, { status: 400 });
    }

    const result = await createFavoriteFolder(folderData);
    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('创建收藏夹失败:', error);
    return NextResponse.json({ error: '创建收藏夹失败' }, { status: 500 });
  }
}

// PUT: /api/database/favorite_folder?id=
export async function PUT(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json({ error: 'Missing id parameter' }, { status: 400 });
  }

  try {
    const updates = await req.json();
    const result = await updateFavoriteFolder(Number(id), updates);
    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('更新收藏夹失败:', error);
    return NextResponse.json({ error: '更新收藏夹失败' }, { status: 500 });
  }
}

// DELETE: /api/database/favorite_folder?id=
export async function DELETE(req: NextRequest) {
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id');

  if (!id) {
    return NextResponse.json({ error: 'Missing id parameter' }, { status: 400 });
  }

  try {
    const result = await deleteFavoriteFolder(Number(id));
    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    console.error('删除收藏夹失败:', error);
    return NextResponse.json({ error: '删除收藏夹失败' }, { status: 500 });
  }
}